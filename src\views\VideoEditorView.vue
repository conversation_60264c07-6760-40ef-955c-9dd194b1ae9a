<template>
  <div class="video-editor">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-section">
        <button @click="importVideo" class="btn btn-primary">
          <i class="icon">📁</i>
          导入视频
        </button>
        <div v-if="selectedClip" class="selected-clip-info">
          <span>已选中: {{ selectedClip.id }}</span>
          <!-- 定格帧时长调整 -->
          <div v-if="isSelectedClipFreezeFrame()" class="freeze-duration-control">
            <label>时长:</label>
            <input type="number" :value="selectedClip.duration" @input="updateFreezeFrameDuration($event)" min="0.1"
              max="60" step="0.1" class="duration-input" />
            <span>秒</span>
          </div>
          <button @click="selectedClip = null" class="btn-clear">×</button>
        </div>
      </div>

      <div class="toolbar-section">
        <button @click="exportVideo" class="btn btn-export" :disabled="isExporting">
          <i class="icon">💾</i>
          {{ isExporting ? '导出中...' : '导出视频' }}
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 预览窗口 -->
      <div class="preview-panel">
        <div class="preview-container">
          <canvas ref="previewCanvas" class="preview-canvas" :width="previewWidth" :height="previewHeight"
            @mousedown="startPreviewDrag" @wheel="handlePreviewWheel"></canvas>
        </div>
      </div>

      <!-- 素材库面板 -->
      <div class="assets-panel">
        <h3>素材库</h3>
        <div class="assets-list">
          <div v-for="asset in assets" :key="asset.id" class="asset-item" @click="selectAsset(asset)"
            :class="{ selected: selectedAsset?.id === asset.id }" draggable="true"
            @dragstart="startDragAsset(asset, $event)">
            <div class="asset-thumbnail">
              <i class="icon">{{ asset.type === 'video' ? '🎬' : '🎵' }}</i>
            </div>
            <div class="asset-info">
              <div class="asset-name">{{ asset.name }}</div>
              <div class="asset-duration">{{ formatTime(asset.duration) }}</div>
            </div>
            <button class="delete-asset-btn" @click.stop="deleteAsset(asset.id)" title="删除素材">
              ×
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 时间轴面板 -->
    <div class="timeline-panel">
      <div class="timeline-header">
        <div class="timeline-left-section">
          <h3>时间轴</h3>
        </div>

        <div class="timeline-center-section">
          <div class="playback-controls">
            <button @click="togglePlay" class="btn btn-small btn-play" :class="{ active: isPlaying }">
              <i class="icon">{{ isPlaying ? '⏸️' : '▶️' }}</i>
            </button>
            <button @click="stop" class="btn btn-small">
              <i class="icon">⏹️</i>
            </button>
          </div>

          <div class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</div>

          <button @click="openTextEditor" class="btn btn-small btn-primary">
            <i class="icon">📝</i>
            添加文字
          </button>
        </div>

        <div class="timeline-right-section">
          <div class="timeline-controls">
            <button @click="createFreezeFrame" class="btn btn-small freeze-btn" :disabled="!selectedClip"
              title="创建定格帧（需要先选择片段）">
              📸 定格
            </button>
            <button @click="zoomIn" class="btn btn-small" title="放大时间轴">🔍+</button>
            <span class="zoom-level">{{ Math.round(timelineZoom * 100) }}%</span>
            <button @click="zoomOut" class="btn btn-small" title="缩小时间轴">🔍-</button>
          </div>
        </div>
      </div>

      <div class="timeline-container" ref="timelineContainer">
        <!-- 时间轴内容容器，具有固定宽度 -->
        <div class="timeline-content" :style="{ width: getTimelineContentWidth() + 'px' }">
          <!-- 时间标尺 -->
          <div class="timeline-ruler-row">
            <div class="ruler-header">
              <span>时间</span>
            </div>
            <div class="ruler-content" @click="handleTimelineClick">
              <div v-for="mark in timeMarks" :key="mark.time" class="time-mark" :style="{ left: mark.position + 'px' }">
                {{ formatTime(mark.time) }}
              </div>
            </div>
          </div>

          <!-- 播放头 -->
          <div class="playhead" :style="{ left: playheadPosition + 'px' }" @mousedown="startDragPlayhead"></div>

          <!-- 视频轨道 -->
          <div v-for="(track, trackIndex) in videoTracks" :key="track.id" class="track video-track">
            <div class="track-header">
              <span>轨道 {{ trackIndex + 1 }}</span>
              <!-- <button v-if="videoTracks.length > 1" class="delete-track-btn" @click="deleteVideoTrack(track.id)"
              title="删除轨道">
              ×
            </button> -->
            </div>
            <div class="track-content" @drop="dropOnTrack($event, track.id, 'video')" @dragover.prevent
              @dragenter.prevent>
              <div v-for="clip in track.clips" :key="`${clip.id}-${uiRefreshKey}`" class="clip video-clip" :class="{
                selected: selectedClip?.id === clip.id,
                'freeze-frame': clip.isFreeze,
              }" :style="getClipStyle(clip)" @mousedown="startDragClip(clip, $event)"
                @click="selectClip(clip, track.id)">
                <!-- 暂时禁用帧预览，避免闪烁 -->
                <div class="clip-simple">
                  <span class="clip-name">{{ clip.name }}</span>
                </div>
                <button class="delete-clip-btn" @click.stop="deleteClip(clip.id, track.id)" title="删除片段">
                  ×
                </button>
                <!-- 调整大小的手柄 -->
                <div class="resize-handle resize-left" @mousedown.stop="startResizeClip(clip, 'left', $event)"></div>
                <div class="resize-handle resize-right" @mousedown.stop="startResizeClip(clip, 'right', $event)"></div>
              </div>
            </div>
          </div>

          <!-- 文字轨道 -->
          <div class="track text-track">
            <div class="track-header">
              <span>文字</span>
            </div>
            <div class="track-content">
              <div v-for="textClip in textClips" :key="`text-${textClip.id}-${uiRefreshKey}`" class="clip text-clip"
                :class="{ selected: selectedClip?.id === textClip.id }" :style="{
                  left: getTimelinePosition(textClip.startTime) + 'px',
                  width:
                    getTimelineWidth() * (textClip.duration / Math.max(duration, 60)) * timelineZoom +
                    'px',
                }" @click="selectTextClip(textClip)" @mousedown.stop="startDragClip(textClip, $event)">
                <span class="clip-content">{{ textClip.content }}</span>
                <button v-if="selectedClip?.id === textClip.id" @click.stop="deleteTextClip(textClip.id)"
                  class="delete-clip-btn" title="删除文字">×</button>

                <!-- 调整大小的手柄 -->
                <div class="resize-handle resize-left" @mousedown.stop="startResizeTextClip(textClip, 'left', $event)">
                </div>
                <div class="resize-handle resize-right"
                  @mousedown.stop="startResizeTextClip(textClip, 'right', $event)">
                </div>
              </div>
            </div>
          </div>

          <!-- 添加视频轨道按钮 -->
          <!-- <div class="add-track-section">
          <button @click="addVideoTrack" class="btn btn-small">+ 添加视频轨道</button>
        </div> -->
        </div> <!-- 关闭 timeline-content -->
      </div>
    </div>

    <!-- 文件输入 -->
    <input ref="videoInput" type="file" accept="video/*" @change="handleVideoImport" style="display: none" multiple />

    <!-- 文字编辑器模态框 -->
    <div v-if="showTextEditor" class="modal-overlay" @click="cancelTextEdit">
      <div class="modal-content" @click.stop>
        <h3>{{ editingTextClip ? '编辑文字' : '添加文字' }}</h3>

        <div class="form-group">
          <label>文字内容：</label>
          <textarea v-model="textForm.content" placeholder="输入文字内容"></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>字体大小：</label>
            <input type="number" v-model.number="textForm.fontSize" min="12" max="200" />
          </div>

          <div class="form-group">
            <label>字体：</label>
            <select v-model="textForm.fontFamily">
              <option value="Arial">Arial</option>
              <option value="微软雅黑">微软雅黑</option>
              <option value="宋体">宋体</option>
              <option value="黑体">黑体</option>
              <option value="楷体">楷体</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>颜色：</label>
            <input type="color" v-model="textForm.color" />
          </div>

          <div class="form-group">
            <label>对齐：</label>
            <select v-model="textForm.textAlign">
              <option value="left">左对齐</option>
              <option value="center">居中</option>
              <option value="right">右对齐</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>X位置：</label>
            <input type="range" v-model.number="textForm.x" min="0" max="1" step="0.01" />
            <span>{{ (textForm.x * 100).toFixed(0) }}%</span>
          </div>

          <div class="form-group">
            <label>Y位置：</label>
            <input type="range" v-model.number="textForm.y" min="0" max="1" step="0.01" />
            <span>{{ (textForm.y * 100).toFixed(0) }}%</span>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>开始时间（秒）：</label>
            <input type="number" v-model.number="textForm.startTime" min="0" step="0.1" />
          </div>

          <div class="form-group">
            <label>持续时间（秒）：</label>
            <input type="number" v-model.number="textForm.duration" min="0.1" step="0.1" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>透明度：</label>
            <input type="range" v-model.number="textForm.opacity" min="0" max="1" step="0.01" />
            <span>{{ (textForm.opacity * 100).toFixed(0) }}%</span>
          </div>

          <div class="form-group">
            <label>旋转角度：</label>
            <input type="range" v-model.number="textForm.rotation" min="-180" max="180" step="1" />
            <span>{{ textForm.rotation }}°</span>
          </div>
        </div>

        <div class="modal-buttons">
          <button @click="cancelTextEdit" class="btn btn-secondary">取消</button>
          <button @click="editingTextClip ? updateTextClip() : addTextClip()" class="btn btn-primary">
            {{ editingTextClip ? '更新' : '添加' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 导出进度模态框 -->
    <div v-if="showExportProgress" class="modal-overlay">
      <div class="modal-content export-progress-modal" @click.stop>
        <h3>正在导出视频</h3>

        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: exportProgress + '%' }"></div>
          </div>
          <div class="progress-text">{{ exportProgress }}%</div>
        </div>

        <div class="progress-status">
          <span v-if="exportProgress < 5">正在初始化...</span>
          <span v-else-if="exportProgress < 90">正在录制视频 ({{ formatTime(exportCurrentTime) }} /
            {{ formatTime(exportTotalTime) }})</span>
          <span v-else-if="exportProgress < 100">正在处理文件...</span>
          <span v-else>导出完成！</span>
        </div>

        <div class="export-details">
          <p>预计文件大小: {{ estimatedFileSize }}</p>
          <p>已用时间: {{ formatTime(exportElapsedTime) }}</p>
          <p v-if="exportProgress > 5 && exportProgress < 90">
            预计剩余: {{ formatTime(exportEstimatedRemaining) }}
          </p>
        </div>

        <!-- 只有在导出完成时才显示关闭按钮 -->
        <div v-if="exportProgress >= 100" class="modal-buttons">
          <button @click="closeExportProgress" class="btn btn-primary">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { VideoEditor, type EditorState } from '../core/VideoEditor'
import { ElMessage, ElLoading } from 'element-plus'
// 响应式数据
const previewCanvas = ref<HTMLCanvasElement>()
const timelineContainer = ref<HTMLDivElement>()
const videoInput = ref<HTMLInputElement>()
const audioInput = ref<HTMLInputElement>()

const previewWidth = 1920
const previewHeight = 1080
const currentTime = ref(0)
const duration = ref(0)
const isPlaying = ref(false)
const isExporting = ref(false)
const timelineZoom = ref(1)
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024)

// 编辑器实例
let editor: VideoEditor | null = null

// 素材和轨道数据
const assets = ref<any[]>([])
const selectedAsset = ref<any>(null)
const videoTracks = ref<any[]>([
  { id: 'track-1', clips: [] },
  { id: 'track-2', clips: [] }, // 添加第二个轨道用于多视频合成
])

// 拖拽状态
const isDragging = ref(false)
const dragType = ref<'playhead' | 'clip' | 'asset' | 'resize' | null>(null)
const dragStartX = ref(0)
const dragStartTime = ref(0)
const draggedItem = ref<any>(null)
const resizeDirection = ref<'left' | 'right' | null>(null)
const draggedAsset = ref<any>(null) // 存储正在拖拽的素材对象
const uiRefreshKey = ref(0) // 用于强制重新渲染
const showTextEditor = ref(false) // 显示文字编辑器
const editingTextClip = ref<any>(null) // 正在编辑的文字片段
const textClips = ref<any[]>([]) // 文字片段列表
const textForm = ref({
  content: '示例文字',
  fontSize: 32,
  fontFamily: 'Arial',
  color: '#ffffff',
  x: 0.5, // 屏幕中央
  y: 0.5, // 屏幕中央
  opacity: 1.0,
  rotation: 0,
  textAlign: 'center' as const,
  fontWeight: 'normal' as const,
  fontStyle: 'normal' as const,
  startTime: 0,
  duration: 3,
})

// 导出进度相关状态
const showExportProgress = ref(false) // 显示导出进度模态框
const exportProgress = ref(0) // 导出进度百分比
const exportCurrentTime = ref(0) // 当前导出时间
const exportTotalTime = ref(0) // 总导出时间
const exportStartTime = ref(0) // 导出开始时间戳
const exportElapsedTime = ref(0) // 已用时间
const exportEstimatedRemaining = ref(0) // 预计剩余时间
const estimatedFileSize = ref('计算中...')

// 预览拖拽状态
const isPreviewDragging = ref(false)
const previewDragStart = ref({ x: 0, y: 0 })
const selectedClip = ref<any>(null) // 当前选中的片段

// 计算属性
const trackHeaderWidth = computed(() => {
  return windowWidth.value <= 768 ? 80 : 120
})

const playheadPosition = computed(() => {
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60) // 至少60秒的时间轴
  // 播放头应该从轨道标题之后开始
  return (currentTime.value / totalDuration) * timelineWidth + trackHeaderWidth.value
})

const timeMarks = computed(() => {
  const marks = []
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60) // 至少显示60秒
  // 使用计算属性中的trackHeaderWidth
  const interval = totalDuration / 10 // 10个标记

  for (let i = 0; i <= 10; i++) {
    const time = i * interval
    const position = (time / totalDuration) * timelineWidth
    marks.push({ time, position })
  }

  return marks
})

// 生命周期
onMounted(async () => {
  await nextTick()
  if (previewCanvas.value) {
    initializeEditor()
  }

  // 添加用户交互监听器，确保AudioContext在线上环境中能正确启动
  setupAudioContextUserInteraction()
  const data = {
    "name": "merge-video",
    list: [
      {
        "video_url": "https://f.zhixuee.com/ai_video/3061753708323.webm",
        "page": 1
      },
      {
        "video_url": "https://f.zhixuee.com/ai_video/3071753708371.webm",
        "page": 2
      },
      {
        "video_url": "https://f.zhixuee.com/ai_video/3081753708440.webm",
        "page": 3
      },
      {
        "video_url": "https://f.zhixuee.com/ai_video/3121753772613.webm",
        "page": 4
      },
      {
        "video_url": "https://f.zhixuee.com/ai_video/3101753708735.webm",
        "page": 5
      }
    ]
  }
  handleMergeVideoMessage(data.list)

  // 设置iframe通信
  // setupIframeMessaging()

  // 添加窗口大小变化监听
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', handleWindowResize)
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

// 窗口大小变化监听
function handleWindowResize() {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth
  }
}

onUnmounted(() => {
  if (editor) {
    editor.destroy()
  }

  // 清理事件监听器
  if (typeof window !== 'undefined') {
    window.removeEventListener('message', handlePostMessage)
    window.removeEventListener('resize', handleWindowResize)
  }

  // 清理键盘事件监听
  document.removeEventListener('keydown', handleKeyDown)
})

// 方法
function handleKeyDown(event: KeyboardEvent) {
  // 检查是否在输入框中，如果是则不处理
  const target = event.target as HTMLElement
  if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
    return
  }

  // 处理删除键
  if (event.key === 'Delete' || event.key === 'Backspace') {
    if (selectedClip.value) {
      event.preventDefault()

      // 检查是否是文字片段
      const isTextClip = textClips.value.some(clip => clip.id === selectedClip.value?.id)

      if (isTextClip) {
        // 删除文字片段
        deleteTextClip(selectedClip.value.id)
      } else {
        // 删除视频片段
        // 找到片段所在的轨道
        for (const track of videoTracks.value) {
          const clipIndex = track.clips.findIndex((c: any) => c.id === selectedClip.value?.id)
          if (clipIndex !== -1) {
            deleteClip(selectedClip.value.id, track.id)
            break
          }
        }
      }
    }
  }
}

function initializeEditor() {
  if (!previewCanvas.value) return

  editor = new VideoEditor({
    canvas: previewCanvas.value,
    width: previewWidth,
    height: previewHeight,
    frameRate: 30,
  })

  // 监听状态变化
  editor.onStateChange((state: EditorState) => {
    currentTime.value = state.currentTime
    duration.value = state.duration
    isPlaying.value = state.isPlaying
  })
}

// 设置AudioContext用户交互监听器（解决线上环境音频问题）
function setupAudioContextUserInteraction() {
  let audioContextActivated = false

  const activateAudioContext = async () => {
    if (audioContextActivated || !editor) return

    try {

      const audioMixer = editor.getAudioMixer()
      if (audioMixer) {
        await audioMixer.ensureAudioContextRunning()
        audioContextActivated = true


        // 移除事件监听器，避免重复激活
        document.removeEventListener('click', activateAudioContext)
        document.removeEventListener('keydown', activateAudioContext)
        document.removeEventListener('touchstart', activateAudioContext)
      }
    } catch (error) {

    }
  }

  // 监听用户交互事件
  document.addEventListener('click', activateAudioContext, { once: true })
  document.addEventListener('keydown', activateAudioContext, { once: true })
  document.addEventListener('touchstart', activateAudioContext, { once: true })


}

// iframe通信相关函数
function setupIframeMessaging() {
  // 监听来自父页面的消息
  window.addEventListener('message', handlePostMessage)

  // 通知父页面我们已经准备好了
  if (window.parent !== window) {
    window.parent.postMessage(
      {
        type: 'editor_ready',
      },
      '*',
    )
  }
}

function handlePostMessage(event: MessageEvent) {
  try {
    const data = JSON.parse(event?.data)


    // 检查是否是合并视频的消息
    if (data.name === 'merge-video' && data.list && Array.isArray(data.list)) {
      handleMergeVideoMessage(data.list)
    }
  } catch (error) { }
}

async function handleMergeVideoMessage(
  videoList: Array<{ video_url: string; img_url: string; page: number }>,
) {
  if (!editor) {
    return
  }

  try {
    // 按page排序
    const sortedVideos = [...videoList].sort((a, b) => a.page - b.page)

    let currentTime = 0 // 累计时间，用于顺序排列

    for (const videoItem of sortedVideos) {
      try {
        // 从URL加载视频
        const asset = await loadVideoFromUrl(videoItem.video_url, `人像素材${videoItem.page}`)

        // 将视频添加到素材库
        assets.value.push(asset)

        // 添加到轨道二（track-2）
        await addVideoToTrack(asset, 'track-2', currentTime)

        // 更新累计时间
        currentTime += asset.duration
      } catch (error) {
        // 继续处理其他视频，不中断整个流程
      }
    }

    // 设置播放时间到第一个视频的开始时间，确保有活跃片段
    if (sortedVideos.length > 0) {
      editor.setCurrentTime(0) // 设置到第一个视频开始位置

      // 启动初始预加载，确保第一次播放流畅
      setTimeout(() => {
        performInitialPreloading()
      }, 500) // 延迟500ms，让视频元素有时间初始化
    }

    // 通知父页面处理完成
    if (window.parent !== window) {
      window.parent.postMessage(
        {
          type: 'video-merge-complete',
          success: true,
          videosProcessed: sortedVideos.length,
        },
        '*',
      )
    }
  } catch (error) {
    // 通知父页面处理失败
    if (window.parent !== window) {
      window.parent.postMessage(
        {
          type: 'video-merge-complete',
          success: false,
          error: error instanceof Error ? error.message : String(error),
        },
        '*',
      )
    }
  }
}

// 执行初始预加载，确保第一次播放流畅
function performInitialPreloading() {
  if (!editor) return



  // 获取所有轨道的前几个视频片段
  const allTracks = editor.getAllVideoTracks()
  allTracks.forEach((track, trackId) => {
    const allClips = track.getAllClips()
    // 预加载前3个片段
    const clipsToPreload = allClips.slice(0, 3)

    clipsToPreload.forEach((clip, index) => {
      const video = clip.videoElement
      const isFreezeFrame = (video as any)._isFreezeFrame

      if (isFreezeFrame) return // 跳过定格帧

      // 设置积极的预加载策略
      video.preload = 'auto'

      // 延迟加载，避免同时加载太多视频
      setTimeout(() => {
        if (video.readyState < video.HAVE_CURRENT_DATA) {
          video.load()

        }

        // 预设到正确的时间点
        setTimeout(() => {
          try {
            video.currentTime = clip.trimStart
          } catch (error) {
            // 忽略时间设置错误
          }
        }, 100)
      }, index * 200) // 每个片段延迟200ms
    })
  })
}

async function loadVideoFromUrl(url: string, name: string): Promise<any> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    // 恢复crossOrigin设置，WebGL渲染需要CORS支持
    video.crossOrigin = 'anonymous'
    video.preload = 'auto' // 改为auto，更积极的预加载
    video.muted = true // 静音视频元素，避免直接播放音频
    video.volume = 1.0 // 设置音量为1，让AudioMixer能获取音频数据

    const timeoutId = setTimeout(() => {
      video.removeEventListener('loadedmetadata', onLoaded)
      video.removeEventListener('error', onError)
      reject(new Error(`加载视频超时: ${url}`))
    }, 30000) // 30秒超时

    const onLoaded = () => {
      clearTimeout(timeoutId)
      video.removeEventListener('error', onError)

      const asset = {
        id: Math.random().toString(36).substr(2, 9),
        name: name,
        type: 'video',
        url: url,
        duration: video.duration,
        file: null, // URL加载的视频没有File对象
        videoElement: video,
      }

      resolve(asset)
    }

    const onError = () => {
      clearTimeout(timeoutId)
      video.removeEventListener('loadedmetadata', onLoaded)
      reject(new Error(`加载视频失败: ${url}`))
    }

    video.addEventListener('loadedmetadata', onLoaded)
    video.addEventListener('error', onError)
    video.src = url
  })
}

// 添加URL素材到轨道的专用函数
async function addUrlAssetToTrack(asset: any, trackId: string, startTime: number): Promise<string> {
  if (!editor) {
    throw new Error('编辑器未初始化')
  }

  const clipId = Math.random().toString(36).substr(2, 9)

  // 创建VideoClip对象
  const videoClip = {
    id: clipId,
    videoElement: asset.videoElement,
    name: asset.name, // 保存素材名称
    startTime: startTime,
    duration: asset.duration,
    trimStart: 0,
    trimEnd: asset.duration,
    opacity: 1.0,
    volume: 1.0,
    muted: false,
    transform: {
      x: 0.5, // 居中
      y: 0.5, // 居中
      scaleX: 1.0, // 原始大小
      scaleY: 1.0, // 原始大小
      rotation: 0, // 无旋转
    },
    effects: [],
  }

  // 获取指定轨道并添加片段
  const videoTracksMap = editor.getAllVideoTracks()
  const track = videoTracksMap.get(trackId)
  if (!track) {
    throw new Error(`轨道 ${trackId} 不存在`)
  }

  track.addClip(videoClip)

  // 如果视频包含音频，添加到音频混合器
  try {
    if (hasAudioTrack(asset.videoElement)) {
      const audioElement = new Audio()
      // 恢复crossOrigin设置，保持与视频元素的一致性
      audioElement.crossOrigin = 'anonymous'
      audioElement.src = asset.url
      audioElement.preload = 'metadata'

      const audioClip = {
        id: clipId + '_audio',
        audioElement,
        startTime: startTime,
        duration: asset.duration,
        trimStart: 0,
        trimEnd: asset.duration,
        volume: 1.0,
        muted: false,
        fadeIn: 0,
        fadeOut: 0,
        effects: [],
        trackId: trackId, // 设置轨道ID，用于录制时过滤
      }

      const audioMixer = editor.getAudioMixer()
      if (audioMixer) {
        audioMixer.addClip(audioClip)
      }
    }
  } catch (error) {

  }

  // 通知状态变化
  editor.updateState()

  return clipId
}

async function addVideoToTrack(asset: any, trackId: string, startTime: number) {
  if (!editor) {
    throw new Error('编辑器未初始化')
  }

  try {
    // 直接添加VideoClip到轨道，而不是使用addVideoClip方法
    const clipId = Math.random().toString(36).substr(2, 9)

    // 创建VideoClip对象
    const videoClip = {
      id: clipId,
      videoElement: asset.videoElement,
      startTime: startTime,
      duration: asset.duration,
      trimStart: 0,
      trimEnd: asset.duration,
      opacity: 1.0,
      volume: 1.0,
      muted: false,
      transform: {
        x: 0.07, // 左下角：距离左边20%
        y: 0.8, // 左下角：距离顶部80%（即距离底部20%）
        scaleX: 0.4, // 缩小到40%
        scaleY: 0.4, // 缩小到40%
        rotation: 0, // 无旋转
      },
      effects: [],
    }

    // 获取指定轨道并添加片段
    const videoTracksMap = editor.getAllVideoTracks()
    const track = videoTracksMap.get(trackId)
    if (!track) {
      throw new Error(`轨道 ${trackId} 不存在`)
    }

    track.addClip(videoClip)

    // 添加音频轨道（如果视频包含音频）
    try {
      // 检查视频是否有音频轨道
      if (hasAudioTrack(asset.videoElement)) {
        // 创建音频元素
        const audioElement = new Audio()
        // 恢复crossOrigin设置，保持与视频元素的一致性
        audioElement.crossOrigin = 'anonymous'
        audioElement.src = asset.url // 使用相同的URL
        audioElement.preload = 'metadata'

        // 创建音频片段
        const audioClip = {
          id: clipId + '_audio',
          audioElement,
          startTime: startTime,
          duration: asset.duration,
          trimStart: 0,
          trimEnd: asset.duration,
          volume: 1.0,
          muted: false,
          fadeIn: 0,
          fadeOut: 0,
          effects: [],
          trackId: trackId, // 设置轨道ID，用于录制时过滤
        }

        // 添加到音频混合器
        const audioMixer = editor.getAudioMixer()
        if (audioMixer) {
          audioMixer.addClip(audioClip)
        }
      } else {

      }
    } catch (error) {

      // 音频添加失败不影响视频播放
    }

    // 添加到UI轨道
    const uiTrack = videoTracks.value.find((t: any) => t.id === trackId)
    if (uiTrack) {
      const newClip = {
        id: clipId,
        assetId: asset.id,
        name: asset.name,
        startTime: startTime,
        duration: asset.duration,
        trimStart: 0,
        trimEnd: asset.duration,
      }

      uiTrack.clips.push(newClip)

      // 按开始时间排序
      uiTrack.clips.sort((a: any, b: any) => a.startTime - b.startTime)
    }

    // 通知状态变化
    editor.updateState()

    return clipId
  } catch (error) {

    throw error
  }
}

// 辅助函数：检查视频是否包含音频轨道
function hasAudioTrack(videoElement: HTMLVideoElement): boolean {
  // 检查媒体轨道
  if ((videoElement as any).audioTracks && (videoElement as any).audioTracks.length > 0) {
    return true
  }

  // 检查媒体流
  if ((videoElement as any).mozHasAudio === true) {
    return true
  }

  if (
    (videoElement as any).webkitAudioDecodedByteCount &&
    (videoElement as any).webkitAudioDecodedByteCount > 0
  ) {
    return true
  }

  // 默认假设有音频（保守做法）
  return true
}

function importVideo() {
  videoInput.value?.click()
}

// 帧预览相关
const frameCanvases = ref<Map<string, HTMLCanvasElement[]>>(new Map())
const frameGenerationQueue = new Set<string>() // 防止重复生成

// 获取片段宽度（像素）
function getClipWidth(clip: any): number {
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60)
  return (clip.duration / totalDuration) * timelineWidth
}

// 获取片段需要显示的帧数（优化：减少帧数，提高性能）
function getClipFrames(clip: any) {
  const clipWidth = getClipWidth(clip)
  const frameWidth = 80 // 增大每帧宽度，减少帧数
  const frameCount = Math.max(1, Math.min(5, Math.floor(clipWidth / frameWidth))) // 最多5帧
  return Array(frameCount)
    .fill(0)
    .map((_, index) => index)
}

// 设置帧画布引用
function setFrameCanvas(el: any, clipId: string, frameIndex: number) {
  if (!el || !(el instanceof HTMLCanvasElement)) return

  if (!frameCanvases.value.has(clipId)) {
    frameCanvases.value.set(clipId, [])
  }

  const canvases = frameCanvases.value.get(clipId)!
  canvases[frameIndex] = el

  // 延迟生成帧预览，避免阻塞UI
  const key = `${clipId}-${frameIndex}`
  if (!frameGenerationQueue.has(key)) {
    frameGenerationQueue.add(key)
    setTimeout(
      () => {
        generateFramePreview(clipId, frameIndex)
        frameGenerationQueue.delete(key)
      },
      100 + frameIndex * 50,
    ) // 错开生成时间
  }
}

// 生成帧预览（轻量级版本）
function generateFramePreview(clipId: string, frameIndex: number) {
  if (!editor) return

  // 查找对应的VideoClip
  const allTracks = editor.getAllVideoTracks()
  let videoClip: any = null

  allTracks.forEach((track) => {
    if (!videoClip) {
      const foundClip = track.findClipById(clipId)
      if (foundClip) {
        videoClip = foundClip
      }
    }
  })

  if (!videoClip) return

  const canvases = frameCanvases.value.get(clipId)
  if (!canvases || !canvases[frameIndex]) return

  const canvas = canvases[frameIndex]
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 简单填充颜色作为占位符，避免复杂的视频截帧
  const colors = ['#4a90e2', '#5cb85c', '#f0ad4e', '#d9534f', '#5bc0de']
  const color = colors[frameIndex % colors.length]

  ctx.fillStyle = color
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  // 添加帧索引文字
  ctx.fillStyle = 'white'
  ctx.font = '10px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(`${frameIndex + 1}`, canvas.width / 2, canvas.height / 2 + 3)
}

// 选择片段
function selectClip(clip: any, trackId: string) {
  if (!editor) return

  // 查找对应的VideoEditor中的片段 - 在所有轨道中查找
  const allTracks = editor.getAllVideoTracks()
  let videoClip: any = null

  allTracks.forEach((track, currentTrackId) => {
    if (!videoClip) {
      const foundClip = track.findClipById(clip.id)
      if (foundClip) {
        videoClip = foundClip
      }
    }
  })

  if (videoClip) {
    selectedClip.value = videoClip
  } else {
    selectedClip.value = null
  }
}

async function handleVideoImport(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files || files.length === 0 || !editor) return

  // 处理多个文件
  for (let i = 0; i < files.length; i++) {
    const file = files[i]

    try {
      // 验证文件类型
      if (!file.type.startsWith('video/')) {
        ElMessage.warning(`文件 ${file.name} 不是有效的视频文件`)
        continue
      }

      // 创建视频元素来获取时长
      const video = document.createElement('video')
      const objectUrl = URL.createObjectURL(file)
      video.src = objectUrl
      video.preload = 'metadata'
      video.muted = true // 静音视频元素，避免直接播放音频
      video.volume = 1.0 // 设置音量为1，让AudioMixer能获取音频数据

      // 设置超时处理
      const timeoutId = setTimeout(() => {
        URL.revokeObjectURL(objectUrl)
        ElMessage.error(`视频文件 ${file.name} 加载超时`)
      }, 10000) // 10秒超时

      video.onloadedmetadata = () => {
        clearTimeout(timeoutId)
        // 验证视频时长
        if (!video.duration || video.duration <= 0 || !isFinite(video.duration)) {
          ElMessage.error(`视频文件 ${file.name} 的时长无效`)
          URL.revokeObjectURL(video.src)
          return
        }

        // 添加到素材库
        const asset = {
          id: `asset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: file.name,
          type: 'video',
          duration: video.duration,
          file: file, // 确保File对象被正确保存
        }

        assets.value.push(asset)
        URL.revokeObjectURL(objectUrl)
      }

      video.onerror = (event) => {
        clearTimeout(timeoutId)
        URL.revokeObjectURL(objectUrl)
        ElMessage.error(`视频文件 ${file.name} 加载失败: ${video.error?.message || '未知错误'}`)
      }
    } catch (error) {
      ElMessage.error(`导入视频 ${file.name} 失败`)
    }
  }

  // 清空输入
  target.value = ''
}

async function togglePlay() {
  if (!editor) return

  if (isPlaying.value) {

    editor.pause()
  } else {

    try {
      // 等待音频准备完成后再播放
      await editor.play()

    } catch (error) {

    }
  }
}

function stop() {
  if (!editor) return

  editor.pause()
  editor.setCurrentTime(0)
}

async function exportVideo() {
  if (!editor || isExporting.value) return

  isExporting.value = true
  showExportProgress.value = true
  exportProgress.value = 0
  exportStartTime.value = Date.now()
  exportTotalTime.value = editor.getTotalDuration()
  exportCurrentTime.value = 0
  exportElapsedTime.value = 0
  exportEstimatedRemaining.value = 0
  estimatedFileSize.value = '计算中...'

  // 启动进度更新定时器
  const progressTimer = setInterval(() => {
    if (showExportProgress.value) {
      const elapsed = (Date.now() - exportStartTime.value) / 1000
      exportElapsedTime.value = elapsed

      // 更新当前导出时间（基于进度估算）
      if (exportProgress.value > 5 && exportProgress.value < 90) {
        const recordingProgress = (exportProgress.value - 5) / 85 // 5%-90%的录制进度
        exportCurrentTime.value = recordingProgress * exportTotalTime.value

        // 估算剩余时间
        if (recordingProgress > 0.1) {
          // 至少有10%的进度才估算
          const recordingElapsed = elapsed * 0.9 // 假设90%时间用于录制
          const totalEstimated = recordingElapsed / recordingProgress
          exportEstimatedRemaining.value = Math.max(0, totalEstimated - elapsed)
        }

        // 估算文件大小（粗略估算）
        const bitrate = 2.5 // Mbps
        const estimatedSizeMB = (exportTotalTime.value * bitrate * 1024) / 8 / 1024
        estimatedFileSize.value = `约 ${estimatedSizeMB.toFixed(1)} MB`
      }
    }
  }, 500)

  try {
    const blob = await editor.exportVideo((progress: number) => {
      exportProgress.value = progress
    })

    clearInterval(progressTimer)

    if (blob.size === 0) {
      throw new Error('导出的视频文件为空，请检查是否有视频内容')
    }

    // 更新实际文件大小
    estimatedFileSize.value = `${(blob.size / 1024 / 1024).toFixed(2)} MB`

    // 确定文件扩展名
    const extension = blob.type.includes('webm') ? 'webm' : 'mp4'
    const filename = `video-export-${Date.now()}.${extension}`

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    // 检查音频编码类型并给出相应提示
    const audioCodecInfo = blob.type.includes('aac') || blob.type.includes('mp4a') ?
      '（使用AAC音频编码，兼容性佳）' :
      blob.type.includes('vorbis') ?
        '（使用Vorbis音频编码，兼容性良好）' :
        '（音频编码已优化）'

    ElMessage.success(`视频导出成功！文件大小：${(blob.size / 1024 / 1024).toFixed(2)} MB ${audioCodecInfo}`)
  } catch (error) {
    clearInterval(progressTimer)
    showExportProgress.value = false
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    ElMessage.error(`导出失败：${errorMessage}`)
  } finally {
    isExporting.value = false
  }
}

// 关闭导出进度对话框
function closeExportProgress() {
  showExportProgress.value = false
  exportProgress.value = 0
}

function selectAsset(asset: any) {
  selectedAsset.value = asset
}

// 删除素材
function deleteAsset(assetId: string) {
  const index = assets.value.findIndex((a) => a.id === assetId)
  if (index !== -1) {
    // 同时从编辑器中移除
    if (editor) {
      editor.removeClip(assetId)
    }

    // 从所有轨道中移除相关片段
    videoTracks.value.forEach((track) => {
      track.clips = track.clips.filter((clip: any) => clip.assetId !== assetId)
    })

    assets.value.splice(index, 1)

    if (selectedAsset.value?.id === assetId) {
      selectedAsset.value = null
    }
  }
}

// 开始拖拽素材
function startDragAsset(asset: any, event: DragEvent) {
  // 将素材对象存储在组件状态中，而不是通过dataTransfer传输
  draggedAsset.value = asset

  if (event.dataTransfer) {
    // 只传输基本信息，不包含File对象
    event.dataTransfer.setData(
      'application/json',
      JSON.stringify({
        type: 'asset',
        assetId: asset.id,
        assetType: asset.type,
        assetName: asset.name,
      }),
    )
  }
}

// 拖拽到轨道
function dropOnTrack(event: DragEvent, trackId: string, trackType: string) {
  event.preventDefault()

  if (!event.dataTransfer) {
    return
  }

  try {
    const dataString = event.dataTransfer.getData('application/json')
    if (!dataString) {
      return
    }

    const data = JSON.parse(dataString)

    if (data.type === 'asset' && data.assetType === 'video') {
      // 使用存储的素材对象而不是传输的数据
      if (draggedAsset.value && draggedAsset.value.id === data.assetId) {
        addClipToTrack(draggedAsset.value, trackId, event)
      } else {
        ElMessage.warning('拖拽的素材信息丢失，请重试')
      }
    } else {

    }
  } catch (error) {

  } finally {
    // 清理拖拽状态
    draggedAsset.value = null
  }
}

// 添加片段到轨道
async function addClipToTrack(asset: any, trackId: string, event: DragEvent) {
  const track = videoTracks.value.find((t) => t.id === trackId)
  if (!track) {
    ElMessage.warning('找不到指定的轨道')
    return
  }

  if (!editor) {
    ElMessage.warning('编辑器未初始化')
    return
  }

  // 检查素材类型：文件素材或URL素材
  const isFileAsset = !!asset.file
  const isUrlAsset = !!asset.url && !!asset.videoElement

  if (!isFileAsset && !isUrlAsset) {
    ElMessage.warning('素材数据不完整，无法添加到轨道')
    return
  }

  // 计算放置时间
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60)
  // track-content区域的x坐标（已经不包含轨道标题）
  const dropTime = (x / timelineWidth) * totalDuration

  try {
    let clipId: string

    if (isFileAsset) {
      // 文件素材：使用传统的addVideoClip方法
      clipId = await editor.addVideoClip(asset.file, Math.max(0, dropTime), trackId)
    } else {
      // URL素材：直接创建VideoClip并添加到轨道
      clipId = await addUrlAssetToTrack(asset, trackId, Math.max(0, dropTime))
    }

    // 创建UI层面的片段数据
    const newClip = {
      id: clipId,
      assetId: asset.id,
      name: asset.name,
      startTime: Math.max(0, dropTime),
      duration: asset.duration,
      trimStart: 0,
      trimEnd: asset.duration,
    }

    track.clips.push(newClip)

    // 按开始时间排序
    track.clips.sort((a: any, b: any) => a.startTime - b.startTime)
  } catch (error) {
    ElMessage.error(`添加视频片段失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 删除片段
function deleteClip(clipId: string, trackId: string) {
  const track = videoTracks.value.find((t) => t.id === trackId)
  if (track) {
    const index = track.clips.findIndex((c: any) => c.id === clipId)
    if (index !== -1) {
      // 从UI中删除
      track.clips.splice(index, 1)

      // 从VideoEditor引擎中删除
      if (editor) {
        editor.removeClip(clipId)
      }
    }
  }
}

// 添加视频轨道
function addVideoTrack() {
  const newTrackId = `track-${Date.now()}`
  videoTracks.value.push({
    id: newTrackId,
    clips: [],
  })
}

// // 删除视频轨道
// function deleteVideoTrack(trackId: string) {
//   if (videoTracks.value.length <= 1) {
//     ElMessage.warning('至少需要保留一个视频轨道')
//     return
//   }

//   const index = videoTracks.value.findIndex((t) => t.id === trackId)
//   if (index !== -1) {
//     videoTracks.value.splice(index, 1)
//   }
// }

function zoomIn() {
  // 放大时增加每秒的像素数，使素材变宽
  timelineZoom.value = Math.min(timelineZoom.value * 1.5, 20)
}

function zoomOut() {
  // 缩小时减少每秒的像素数，使素材变窄
  timelineZoom.value = Math.max(timelineZoom.value / 1.5, 0.05)
}

function getTimelineWidth(): number {
  // 固定的时间轴宽度，不依赖容器宽度，这样素材宽度就不会因为容器变化而压缩
  const totalDuration = Math.max(duration.value, 60) // 至少60秒的时间轴
  const pixelsPerSecond = 50 * timelineZoom.value // 每秒的像素数，受缩放影响
  return totalDuration * pixelsPerSecond
}

function getTimelineContentWidth(): number {
  // 时间轴内容总宽度 = 时间轴宽度 + 轨道标题宽度
  return getTimelineWidth() + trackHeaderWidth.value
}

function getClipStyle(clip: any) {
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60)
  // 视频片段在track-content内显示，零时间点应该是该区域的最左边（即0px）
  const left = (clip.startTime / totalDuration) * timelineWidth

  // 根据片段类型计算宽度
  let clipDuration
  if (clip.isFreeze) {
    // 定格帧使用duration
    clipDuration = clip.duration
  } else if (clip.hasOwnProperty('content')) {
    // 文字片段使用duration（这种情况不应该发生，因为文字片段有自己的样式计算）
    clipDuration = clip.duration
  } else {
    // 普通视频片段使用trimEnd - trimStart
    clipDuration = clip.trimEnd - clip.trimStart
  }

  const width = (clipDuration / totalDuration) * timelineWidth

  return {
    left: left + 'px',
    width: Math.max(width, 50) + 'px',
    top: '8px !important', // 强制固定位置
    transform: 'translateY(0px)', // 确保垂直位置不变
    zIndex: clip.isFreeze ? 15 : 10, // 定格帧稍微高一点层级，但位置不变
  }
}

// 开始调整片段大小
function startResizeClip(clip: any, direction: 'left' | 'right', event: MouseEvent) {
  dragType.value = 'resize'
  isDragging.value = true
  draggedItem.value = clip
  resizeDirection.value = direction
  dragStartX.value = event.clientX

    // 记录原始时长，用于后续UI同步
    ; (clip as any)._originalDuration = clip.duration

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
}

// 处理调整大小
function handleResize(event: MouseEvent) {
  if (!isDragging.value || dragType.value !== 'resize' || !draggedItem.value) return

  const deltaX = event.clientX - dragStartX.value
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60)
  const deltaTime = (deltaX / timelineWidth) * totalDuration

  const clip = draggedItem.value

  if (resizeDirection.value === 'left') {
    // 检查是否为文字片段（有content属性）
    const isTextClip = clip.hasOwnProperty('content')

    // 调整开始时间和裁剪开始时间
    let newStartTime
    if (isTextClip) {
      // 文字片段允许精确到0，当接近0时强制设为0
      const calculatedTime = clip.startTime + deltaTime
      newStartTime = calculatedTime < 0.1 && calculatedTime > -0.1 ? 0 : Math.max(0, calculatedTime)
      // 文字片段直接更新开始时间，持续时间保持不变
      clip.startTime = newStartTime
    } else {
      // 视频片段限制最小为0
      newStartTime = Math.max(0, clip.startTime + deltaTime)
      // 视频片段需要处理裁剪逻辑
      const timeDiff = newStartTime - clip.startTime
      const newTrimStart = Math.max(0, clip.trimStart + timeDiff)

      if (newTrimStart < clip.trimEnd - 0.1) {
        // 最小0.1秒
        clip.startTime = newStartTime
        clip.trimStart = newTrimStart
      }
    }
  } else if (resizeDirection.value === 'right') {
    // 检查是否为文字片段或定格帧
    const isTextClip = clip.hasOwnProperty('content')
    const isFreezeFrame = clip.isFreeze

    if (isTextClip) {
      // 文字片段：直接调整持续时间
      const newDuration = Math.max(0.1, clip.duration + deltaTime)
      clip.duration = newDuration
    } else if (isFreezeFrame) {
      // 定格帧：调整持续时间，定格帧没有trimEnd概念
      const newDuration = Math.max(0.1, clip.duration + deltaTime)
      clip.duration = newDuration
      clip.trimEnd = newDuration // 保持与duration同步
    } else {
      // 普通视频片段：调整裁剪结束时间
      const newTrimEnd = Math.max(clip.trimStart + 0.1, clip.trimEnd + deltaTime)
      if (newTrimEnd <= clip.duration) {
        // 不能超过原始时长
        clip.trimEnd = newTrimEnd
      }
    }
  }

  dragStartX.value = event.clientX
}

// 停止调整大小
function stopResize() {
  // 如果调整了片段大小，需要更新VideoEditor中的片段
  if (draggedItem.value && editor) {
    const clip = draggedItem.value
    const clipId = clip.id
    const isTextClip = clip.hasOwnProperty('content')
    const isFreezeFrame = clip.isFreeze

    if (isTextClip) {
      // 更新文字片段
      editor.updateTextClip(clipId, {
        startTime: clip.startTime,
        duration: clip.duration,
      })
    } else if (isFreezeFrame) {
      // 更新定格帧持续时间
      const originalDuration = (clip as any)._originalDuration || clip.duration
      const durationChange = clip.duration - originalDuration



      const success = editor.updateFreezeFrameDuration(clipId, clip.duration)
      if (success) {


        // 如果是轨道一的定格帧拖拽调整，需要同步UI
        const targetTrack = videoTracks.value.find(track =>
          track.clips.some((c: any) => c.id === clipId)
        )

        if (targetTrack && targetTrack.id === 'track-1' && Math.abs(durationChange) > 0.001) {


          // 计算原始结束时间
          const freezeClip = targetTrack.clips.find((c: any) => c.id === clipId)
          if (freezeClip) {
            const originalEndTime = freezeClip.startTime + originalDuration

            // 找到需要调整的后续片段
            const subsequentClips = targetTrack.clips.filter((c: any) =>
              c.id !== clipId && c.startTime >= originalEndTime - 0.001
            )



            // 调整后续片段的UI位置
            subsequentClips.forEach((c: any) => {
              const oldStartTime = c.startTime
              c.startTime = Math.max(0, oldStartTime + durationChange)

            })
          }
        }
      } else {

      }
    } else {
      // 视频片段需要更新VideoEditor中的片段
      const allTracks = editor.getAllVideoTracks()
      let updated = false

      allTracks.forEach((track, trackId) => {
        if (!updated) {
          const videoClip = track.findClipById(clipId)
          if (videoClip) {
            videoClip.startTime = clip.startTime
            videoClip.trimStart = clip.trimStart
            videoClip.trimEnd = clip.trimEnd
            updated = true


            // 同步音频片段的裁剪时间
            if (editor && editor.updateAudioClipTrimTimes) {
              const audioUpdated = editor.updateAudioClipTrimTimes(clipId, clip.trimStart, clip.trimEnd)
              if (audioUpdated) {

              }
            }
          }
        }
      })
    }

    // 通知状态变化
    editor.updateState()
  }

  isDragging.value = false
  dragType.value = null
  draggedItem.value = null
  resizeDirection.value = null

  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

function startDragPlayhead(event: MouseEvent) {
  dragType.value = 'playhead'
  isDragging.value = true
  dragStartX.value = event.clientX
  dragStartTime.value = currentTime.value

  // 如果正在播放，直接暂停
  if (editor && isPlaying.value) {
    editor.pause()
  }

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

function startDragClip(clip: any, event: MouseEvent) {
  dragType.value = 'clip'
  isDragging.value = true
  draggedItem.value = clip
  dragStartX.value = event.clientX
  dragStartTime.value = clip.startTime

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

function handleDrag(event: MouseEvent) {
  if (!isDragging.value) return

  const deltaX = event.clientX - dragStartX.value

  if (dragType.value === 'playhead') {
    const timelineWidth = getTimelineWidth()
    const totalDuration = Math.max(duration.value, 60) // 至少60秒的时间轴
    const deltaTime = (deltaX / timelineWidth) * totalDuration
    // 允许播放头在整个时间轴范围内移动，不仅限于当前duration
    const newTime = Math.max(0, Math.min(dragStartTime.value + deltaTime, totalDuration))

    // 拖拽过程中只更新UI，不触发音频准备（性能优化）
    currentTime.value = newTime

    // 存储最终时间，在拖拽结束时统一处理
    draggedItem.value = { finalTime: newTime }
  } else if (dragType.value === 'clip' && draggedItem.value) {
    const timelineWidth = getTimelineWidth()
    const totalDuration = Math.max(duration.value, 60)
    const deltaTime = (deltaX / timelineWidth) * totalDuration

    // 检查是否为文字片段（有content属性）
    const isTextClip = draggedItem.value.hasOwnProperty('content')

    // 文字片段允许移动到零时间点，视频片段仍需限制在0以上
    let newStartTime
    if (isTextClip) {
      // 文字片段允许精确到0，但当接近0时强制设为0
      const calculatedTime = dragStartTime.value + deltaTime
      newStartTime = calculatedTime < 0.1 && calculatedTime > -0.1 ? 0 : Math.max(0, calculatedTime)
    } else {
      // 视频片段限制最小为0
      newStartTime = Math.max(0, dragStartTime.value + deltaTime)
    }

    // 应用吸附逻辑
    newStartTime = applyClipSnapping(draggedItem.value, newStartTime)

    draggedItem.value.startTime = newStartTime
  }
}

async function stopDrag() {
  // 如果拖拽了播放头，需要设置最终时间并准备音频
  if (dragType.value === 'playhead' && draggedItem.value && editor) {
    const finalTime = draggedItem.value.finalTime


    try {
      // 等待时间设置完成，确保音频状态正确
      await editor.setCurrentTime(finalTime)

    } catch (error) {

    }
  }
  // 如果拖拽了片段，需要更新VideoEditor中的片段位置
  else if (dragType.value === 'clip' && draggedItem.value && editor) {
    const clipId = draggedItem.value.id
    const newStartTime = draggedItem.value.startTime

    // 检查是否为文字片段
    const isTextClip = draggedItem.value.hasOwnProperty('content')

    if (isTextClip) {
      // 更新文字片段
      editor.updateTextClip(clipId, { startTime: newStartTime })

    } else {
      // 更新视频片段 - 需要在所有轨道中查找
      const allTracks = editor.getAllVideoTracks()
      let updated = false

      allTracks.forEach((track, trackId) => {
        if (!updated) {
          const videoClip = track.findClipById(clipId)
          if (videoClip) {
            videoClip.startTime = newStartTime
            updated = true

          }
        }
      })

      // 更新音频片段 - 同步视频片段对应的音频时间
      if (editor.updateAudioClipStartTime) {
        const audioUpdated = editor.updateAudioClipStartTime(clipId, newStartTime)
        if (audioUpdated) {

        }
      }
    }

    // 通知状态变化
    editor.updateState()
  }

  isDragging.value = false
  dragType.value = null
  draggedItem.value = null

  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 处理时间轴点击跳转 - 增强音频同步
async function handleTimelineClick(event: MouseEvent) {
  // 如果正在拖拽，不处理点击
  if (isDragging.value) return

  // 如果正在播放，直接暂停
  if (editor && isPlaying.value) {
    editor.pause()
  }

  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60)
  // 时间轴内容区域的x坐标（已经不包含轨道标题）
  const clickTime = (x / timelineWidth) * totalDuration

  if (editor) {
    const targetTime = Math.max(0, Math.min(clickTime, totalDuration))


    try {
      // 等待时间设置完成，确保音频状态正确
      await editor.setCurrentTime(targetTime)

    } catch (error) {

    }
  }
}

function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 预览画布交互功能
function startPreviewDrag(event: MouseEvent) {
  if (!editor) return

  // 如果没有选中的片段，不允许拖拽
  if (!selectedClip.value) {

    return
  }

  const rect = previewCanvas.value?.getBoundingClientRect()
  if (!rect) return

  const x = (event.clientX - rect.left) / rect.width
  const y = (event.clientY - rect.top) / rect.height

  isPreviewDragging.value = true
  previewDragStart.value = { x, y }



  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handlePreviewDrag)
  document.addEventListener('mouseup', stopPreviewDrag)

  event.preventDefault()
}

function handlePreviewDrag(event: MouseEvent) {
  if (!isPreviewDragging.value || !selectedClip.value) return

  const rect = previewCanvas.value?.getBoundingClientRect()
  if (!rect) return

  const x = (event.clientX - rect.left) / rect.width
  const y = (event.clientY - rect.top) / rect.height

  const deltaX = x - previewDragStart.value.x
  const deltaY = y - previewDragStart.value.y

  // 更新片段位置
  selectedClip.value.transform.x = Math.max(0, Math.min(1, selectedClip.value.transform.x + deltaX))
  selectedClip.value.transform.y = Math.max(0, Math.min(1, selectedClip.value.transform.y + deltaY))

  previewDragStart.value = { x, y }
}

function stopPreviewDrag() {
  if (isPreviewDragging.value) {


    // 移除全局事件监听
    document.removeEventListener('mousemove', handlePreviewDrag)
    document.removeEventListener('mouseup', stopPreviewDrag)

    isPreviewDragging.value = false
    selectedClip.value = null
  }
}

function handlePreviewWheel(event: WheelEvent) {
  if (!editor) return

  event.preventDefault()

  // 只对选中的片段进行缩放
  if (!selectedClip.value) {

    return
  }

  const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1
  selectedClip.value.transform.scaleX *= scaleFactor
  selectedClip.value.transform.scaleY *= scaleFactor

  // 限制缩放范围
  selectedClip.value.transform.scaleX = Math.max(
    0.1,
    Math.min(3.0, selectedClip.value.transform.scaleX),
  )
  selectedClip.value.transform.scaleY = Math.max(
    0.1,
    Math.min(3.0, selectedClip.value.transform.scaleY),
  )
}

// 创建定格帧功能
async function createFreezeFrame() {
  if (!editor || !selectedClip.value) {

    return
  }

  try {


    // 使用当前播放时间作为定格时间
    const currentTime = editor.getCurrentTime()

    // 调用VideoEditor的定格功能
    const freezeClipId = await editor.createFreezeFrame(selectedClip.value.id, currentTime)



    // 查找定格片段被添加到哪个轨道，并更新UI
    const allTracks = editor.getAllVideoTracks()
    let freezeClip: any = null
    let targetTrackId: string | null = null

    allTracks.forEach((track, trackId) => {
      if (!freezeClip) {
        const foundClip = track.findClipById(freezeClipId)
        if (foundClip) {
          freezeClip = foundClip
          targetTrackId = trackId
        }
      }
    })

    if (targetTrackId && freezeClip) {
      // 定格帧应该插入在当前播放时间点，紧接在前半段视频后面
      const freezeTimePoint = currentTime

      // 检查定格帧的实际位置是否正确
      if (Math.abs(freezeClip.startTime - freezeTimePoint) > 0.1) {
        // 修正定格帧位置
        freezeClip.startTime = freezeTimePoint
      }

      // 确保后续片段紧接在定格帧后面
      const freezeEndTime = freezeClip.startTime + freezeClip.duration

      // 重新排列后续片段，使其紧接在定格帧后面
      pushSubsequentClips(targetTrackId, freezeEndTime, 0)

      // 重新同步UI轨道与VideoEditor轨道（因为可能有片段被切分）
      syncUITrackWithVideoEditor(targetTrackId)

    }

    // 选中新创建的定格帧
    if (freezeClip) {
      selectedClip.value = freezeClip
    }

    // 定格操作后的预加载优化
    if (targetTrackId) {
      setTimeout(() => {
        performPostFreezePreloading(targetTrackId!)
      }, 300) // 延迟300ms，让UI更新完成
    }
  } catch (error) {

    ElMessage.error(`创建定格帧失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 检查选中的片段是否为定格帧
function isSelectedClipFreezeFrame(): boolean {
  if (!selectedClip.value) return false

  // 检查UI层面的标记
  const allUITracks = videoTracks.value
  for (const track of allUITracks) {
    const uiClip = track.clips.find((c: any) => c.id === selectedClip.value?.id)
    if (uiClip && uiClip.isFreeze) {
      return true
    }
  }

  return false
}

// 更新定格帧持续时间
function updateFreezeFrameDuration(event: Event) {
  if (!editor || !selectedClip.value) return

  const target = event.target as HTMLInputElement
  const newDuration = parseFloat(target.value)

  if (isNaN(newDuration) || newDuration <= 0) {

    return
  }

  try {
    // 记录原始时长，用于计算变化量
    const originalDuration = selectedClip.value.duration
    const durationChange = newDuration - originalDuration



    // 更新VideoEditor中的定格帧持续时间
    const success = editor.updateFreezeFrameDuration(selectedClip.value.id, newDuration)

    if (success) {
      // 找到定格帧所在的UI轨道
      let targetTrack: any = null
      let freezeClip: any = null

      for (const track of videoTracks.value) {
        const uiClip = track.clips.find((c: any) => c.id === selectedClip.value?.id)
        if (uiClip) {
          targetTrack = track
          freezeClip = uiClip
          break
        }
      }

      if (targetTrack && freezeClip) {
        // 更新定格帧的UI信息
        freezeClip.duration = newDuration
        freezeClip.trimEnd = newDuration

        // 如果是轨道一，需要调整后续片段的UI位置
        if (targetTrack.id === 'track-1' && Math.abs(durationChange) > 0.001) {


          // 计算定格帧的原始结束时间
          const originalEndTime = freezeClip.startTime + originalDuration

          // 找到需要调整的后续片段
          const subsequentClips = targetTrack.clips.filter((clip: any) =>
            clip.id !== freezeClip.id && clip.startTime >= originalEndTime - 0.001
          )



          // 调整后续片段的UI位置
          subsequentClips.forEach((clip: any) => {
            const oldStartTime = clip.startTime
            clip.startTime = Math.max(0, oldStartTime + durationChange)

          })
        }
      }


    } else {

      ElMessage.error('更新定格帧时长失败')
    }
  } catch (error) {

    ElMessage.error(`更新定格帧时长失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 同步UI轨道与VideoEditor轨道
function syncUITrackWithVideoEditor(trackId: string) {
  if (!editor) return

  const videoTrack = editor.getAllVideoTracks().get(trackId)
  const uiTrack = videoTracks.value.find((t) => t.id === trackId)

  if (!videoTrack || !uiTrack) {

    return
  }

  // 获取VideoEditor轨道中的所有片段
  const videoClips = videoTrack.getAllClips()


  // 清空UI轨道并重新填充
  uiTrack.clips = []

  videoClips.forEach((videoClip) => {
    const isFreezeFrame = !!(videoClip.videoElement as any)._isFreezeFrame

    // 使用片段的原始名称，如果没有则使用默认名称
    let clipName: string
    if (videoClip.name) {
      clipName = videoClip.name
    } else if (isFreezeFrame) {
      clipName = `定格帧-${videoClip.id.slice(-6)}`
    } else {
      clipName = `片段-${videoClip.id.slice(-6)}`
    }

    const uiClip = {
      id: videoClip.id,
      assetId: `asset-${videoClip.id}`,
      name: clipName,
      startTime: videoClip.startTime,
      duration: videoClip.duration,
      trimStart: videoClip.trimStart,
      trimEnd: videoClip.trimEnd,
      isFreeze: isFreezeFrame,
    }

    uiTrack.clips.push(uiClip)
  })

  // 按开始时间排序
  uiTrack.clips.sort((a: any, b: any) => a.startTime - b.startTime)

  // 强制重新渲染所有片段
  uiRefreshKey.value++
}

// 文字编辑相关函数
function openTextEditor() {
  // 设置默认开始时间为当前时间
  textForm.value.startTime = currentTime.value
  showTextEditor.value = true
}

function addTextClip() {
  if (!editor) return

  const textClip = {
    id: Date.now().toString(),
    ...textForm.value,
    startTime: currentTime.value, // 使用当前播放时间
  }

  editor.addTextClip(textClip)
  textClips.value = editor.getAllTextClips() // 同步UI
  showTextEditor.value = false

  // 重置表单
  textForm.value = {
    content: '示例文字',
    fontSize: 32,
    fontFamily: 'Arial',
    color: '#ffffff',
    x: 0.5,
    y: 0.5,
    opacity: 1.0,
    rotation: 0,
    textAlign: 'center',
    fontWeight: 'normal',
    fontStyle: 'normal',
    startTime: currentTime.value,
    duration: 3,
  }
}

function cancelTextEdit() {
  showTextEditor.value = false
  editingTextClip.value = null
}

function updateTextClip() {
  if (!editor || !editingTextClip.value) return

  editor.updateTextClip(editingTextClip.value.id, textForm.value)
  showTextEditor.value = false
  editingTextClip.value = null
}

function selectTextClip(textClip: any) {
  selectedClip.value = textClip
}

function deleteTextClip(clipId: string) {
  if (!editor) return

  // 从VideoEditor中删除
  editor.removeTextClip(clipId)

  // 同步UI
  textClips.value = editor.getAllTextClips()

  // 如果删除的是当前选中的片段，清除选择
  if (selectedClip.value?.id === clipId) {
    selectedClip.value = null
  }
}

function getTimelinePosition(startTime: number): number {
  const timelineWidth = getTimelineWidth()
  const totalDuration = Math.max(duration.value, 60)
  // 使用计算属性中的trackHeaderWidth

  // 文字片段在track-content内显示，零时间点应该是该区域的最左边（即0px）
  const position = (startTime / totalDuration) * timelineWidth

  return position
}

function startResizeTextClip(textClip: any, direction: 'left' | 'right', event: MouseEvent) {
  // 重用现有的resize逻辑
  startResizeClip(textClip, direction, event)
}

// 片段吸附逻辑
function applyClipSnapping(currentClip: any, newStartTime: number): number {
  const snapThreshold = 0.5 // 吸附阈值：0.5秒内自动吸附
  let snappedTime = newStartTime
  let minDistance = snapThreshold

  // 获取当前片段所在的轨道
  let currentTrackId = null

  // 在视频轨道中查找当前片段
  for (const track of videoTracks.value) {
    if (track.clips.some((clip: any) => clip.id === currentClip.id)) {
      currentTrackId = track.id
      break
    }
  }

  if (!currentTrackId) {
    // 如果在视频轨道中找不到，检查是否是文字片段
    const isTextClip = currentClip.hasOwnProperty('content')
    if (isTextClip) {
      // 文字片段与所有轨道的片段进行吸附检查
      return applyTextClipSnapping(currentClip, newStartTime)
    }
    return newStartTime
  }

  // 获取当前轨道的所有其他片段
  const currentTrack = videoTracks.value.find((t) => t.id === currentTrackId)
  if (!currentTrack) return newStartTime

  const otherClips = currentTrack.clips.filter((clip: any) => clip.id !== currentClip.id)

  // 检查与其他片段的吸附
  otherClips.forEach((otherClip: any) => {
    // 计算当前片段的结束时间
    const currentClipEndTime =
      newStartTime + (currentClip.duration || currentClip.trimEnd - currentClip.trimStart)
    const otherClipEndTime =
      otherClip.startTime + (otherClip.duration || otherClip.trimEnd - otherClip.trimStart)

    // 吸附到其他片段的结束时间（当前片段的开始时间）
    const distanceToOtherEnd = Math.abs(newStartTime - otherClipEndTime)
    if (distanceToOtherEnd < minDistance) {
      snappedTime = otherClipEndTime
      minDistance = distanceToOtherEnd
    }

    // 吸附到其他片段的开始时间（当前片段的结束时间）
    const distanceToOtherStart = Math.abs(currentClipEndTime - otherClip.startTime)
    if (distanceToOtherStart < minDistance) {
      // 计算应该的开始时间，使得结束时间对齐
      const adjustedStartTime =
        otherClip.startTime - (currentClip.duration || currentClip.trimEnd - currentClip.trimStart)
      if (adjustedStartTime >= 0) {
        snappedTime = adjustedStartTime
        minDistance = distanceToOtherStart
      }
    }
  })

  return snappedTime
}

// 文字片段吸附逻辑
function applyTextClipSnapping(currentTextClip: any, newStartTime: number): number {
  const snapThreshold = 0.5
  let snappedTime = newStartTime
  let minDistance = snapThreshold

  // 检查与所有视频轨道片段的吸附
  videoTracks.value.forEach((track) => {
    track.clips.forEach((clip: any) => {
      const clipEndTime = clip.startTime + (clip.duration || clip.trimEnd - clip.trimStart)
      const textClipEndTime = newStartTime + currentTextClip.duration

      // 吸附到视频片段的结束时间
      const distanceToClipEnd = Math.abs(newStartTime - clipEndTime)
      if (distanceToClipEnd < minDistance) {
        snappedTime = clipEndTime
        minDistance = distanceToClipEnd
      }

      // 吸附到视频片段的开始时间
      const distanceToClipStart = Math.abs(textClipEndTime - clip.startTime)
      if (distanceToClipStart < minDistance) {
        const adjustedStartTime = clip.startTime - currentTextClip.duration
        if (adjustedStartTime >= 0) {
          snappedTime = adjustedStartTime
          minDistance = distanceToClipStart
        }
      }
    })
  })

  // 检查与其他文字片段的吸附
  textClips.value.forEach((textClip) => {
    if (textClip.id === currentTextClip.id) return

    const otherTextEndTime = textClip.startTime + textClip.duration
    const currentTextEndTime = newStartTime + currentTextClip.duration

    // 吸附到其他文字片段的结束时间
    const distanceToOtherEnd = Math.abs(newStartTime - otherTextEndTime)
    if (distanceToOtherEnd < minDistance) {
      snappedTime = otherTextEndTime
      minDistance = distanceToOtherEnd
    }

    // 吸附到其他文字片段的开始时间
    const distanceToOtherStart = Math.abs(currentTextEndTime - textClip.startTime)
    if (distanceToOtherStart < minDistance) {
      const adjustedStartTime = textClip.startTime - currentTextClip.duration
      if (adjustedStartTime >= 0) {
        snappedTime = adjustedStartTime
        minDistance = distanceToOtherStart
      }
    }
  })
  return snappedTime
}

// 推挤后续片段的函数
function pushSubsequentClips(trackId: string, insertTime: number, insertDuration: number) {
  if (!editor) return

  // 在VideoEditor中推挤后续片段
  const track = editor.getAllVideoTracks().get(trackId)
  if (!track) return

  const allClips = track.getAllClips()

  // 找到需要推挤的片段（开始时间在插入时间点之后的）
  const tolerance = 0.001
  const clipsToMove = allClips.filter((clip) => clip.startTime > insertTime + tolerance)

  // 按开始时间排序
  clipsToMove.sort((a, b) => a.startTime - b.startTime)

  if (insertDuration === 0) {
    // 如果推挤距离为0，只是确保片段紧接在insertTime之后

    let currentPosition = insertTime

    clipsToMove.forEach((clip) => {
      clip.startTime = currentPosition
      const clipDuration = clip.duration || clip.trimEnd - clip.trimStart
      currentPosition += clipDuration
    })
  } else {

    clipsToMove.forEach((clip) => {
      clip.startTime += insertDuration
    })
  }

  // 也推挤UI层面的片段
  const uiTrack = videoTracks.value.find((t) => t.id === trackId)
  if (uiTrack) {
    const uiClipsToMove = uiTrack.clips.filter(
      (clip: any) => clip.startTime > insertTime + tolerance,
    )

    if (insertDuration === 0) {
      // 重新排列UI片段
      let currentPosition = insertTime
      uiClipsToMove.sort((a: any, b: any) => a.startTime - b.startTime)

      uiClipsToMove.forEach((clip: any) => {
        clip.startTime = currentPosition
        const clipDuration = clip.duration || clip.trimEnd - clip.trimStart
        currentPosition += clipDuration
      })
    } else {
      // 正常推挤UI片段
      uiClipsToMove.forEach((clip: any) => {
        clip.startTime += insertDuration
      })
    }

    // 重新排序
    uiTrack.clips.sort((a: any, b: any) => a.startTime - b.startTime)
  }
}

// 定格操作后的预加载优化
function performPostFreezePreloading(trackId: string) {
  if (!editor) return



  const allTracks = editor.getAllVideoTracks()
  const track = allTracks.get(trackId)
  if (!track) return

  const allClips = track.getAllClips()
  const currentTime = editor.getCurrentTime()

  // 找到当前时间点之后的片段，特别是切分后的片段
  const upcomingClips = allClips.filter((clip) => {
    return clip.startTime >= currentTime - 1.0 // 包括当前时间前1秒的片段
  }).slice(0, 5) // 只处理接下来的5个片段

  upcomingClips.forEach((clip, index) => {
    const video = clip.videoElement
    const isFreezeFrame = (video as any)._isFreezeFrame
    const isCloned = (video as any)._isCloned

    if (isFreezeFrame) return // 跳过定格帧

    // 对切分后的片段进行特殊处理
    if (isCloned) {


      // 延迟处理，避免阻塞UI
      setTimeout(() => {
        // 强制重新加载
        video.preload = 'auto'
        video.load()

        // 多次尝试确保加载成功
        const ensureLoaded = () => {
          if (video.readyState < video.HAVE_CURRENT_DATA) {
            video.load()
            setTimeout(ensureLoaded, 200)
          } else {
            // 预设到正确的时间点
            try {
              video.currentTime = clip.trimStart
            } catch (error) {
              // 忽略时间设置错误
            }
          }
        }

        setTimeout(ensureLoaded, 100)
      }, index * 150) // 每个片段延迟150ms
    } else {
      // 普通片段的预加载
      if (video.readyState < video.HAVE_ENOUGH_DATA) {
        setTimeout(() => {
          video.preload = 'auto'
          video.load()
        }, index * 100)
      }
    }
  })
}
</script>

<style scoped>
.video-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  min-height: 60px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background-color: #3a3a3a;
  color: #ffffff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn:hover {
  background-color: #4a4a4a;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007acc;
}

.btn-primary:hover {
  background-color: #0066aa;
}

.btn-secondary {
  background-color: #6a4c93;
}

.btn-secondary:hover {
  background-color: #5a3c83;
}

.btn-play.active {
  background-color: #28a745;
}

.btn-export {
  background-color: #dc3545;
}

.btn-export:hover {
  background-color: #c82333;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.icon {
  font-size: 16px;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  min-height: 0;
  position: relative;
}

/* 预览面板 */
.preview-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  border-right: 1px solid #3a3a3a;
  position: relative;
  height: 100%;
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
}

.preview-canvas {
  max-width: 100%;
  max-height: calc(100% - 60px);
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  background-color: #000000;
  cursor: grab;
}

.preview-canvas:active {
  cursor: grabbing;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
  padding: 12px;
  background-color: #2a2a2a;
  border-radius: 6px;
}

/* 素材库面板 */
.assets-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #2a2a2a;
  border-left: 1px solid #3a3a3a;
  min-width: 250px;
}

.assets-panel h3 {
  padding: 16px;
  margin: 0;
  border-bottom: 1px solid #3a3a3a;
  font-size: 16px;
  font-weight: 600;
}

.assets-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.asset-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.asset-item:hover {
  background-color: #3a3a3a;
}

.asset-item.selected {
  background-color: #007acc;
}

.asset-item {
  position: relative;
}

.delete-asset-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background-color: #ff4444;
  color: white;
  font-size: 12px;
  cursor: pointer;
  display: none;
  align-items: center;
  justify-content: center;
}

.asset-item:hover .delete-asset-btn {
  display: flex;
}

.asset-thumbnail {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3a3a3a;
  border-radius: 4px;
  font-size: 20px;
}

.asset-info {
  flex: 1;
  min-width: 0;
}

.asset-name {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.asset-duration {
  font-size: 12px;
  color: #cccccc;
  margin-top: 2px;
}

/* 时间轴面板 */
.timeline-panel {
  background-color: #2a2a2a;
  border-top: 1px solid #3a3a3a;
  display: flex;
  flex-direction: column;
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #3a3a3a;
  min-height: 60px;
  background-color: #2a2a2a;
}

.timeline-left-section {
  flex: 0 0 auto;
}

.timeline-left-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.timeline-center-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.timeline-right-section {
  flex: 0 0 auto;
}

.playback-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.time-display {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #cccccc;
  background-color: #1a1a1a;
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #3a3a3a;
  min-width: 120px;
  text-align: center;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-level {
  color: #ccc;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
}

.freeze-btn {
  background-color: #ff6b6b;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.freeze-btn:hover:not(:disabled) {
  background-color: #ee5a52;
  transform: translateY(-1px);
}

.freeze-btn:disabled {
  background-color: #666;
  color: #999;
  cursor: not-allowed;
  opacity: 0.5;
}

.freeze-btn:disabled:hover {
  transform: none;
}

/* 确保按钮在小屏幕上也能正常显示 */
@media (max-width: 768px) {
  .timeline-controls {
    gap: 4px;
  }

  .freeze-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}

.timeline-container {
  flex: 1;
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  background-color: #1a1a1a;
}

.timeline-content {
  position: relative;
  min-width: 100%;
}

/* 时间标尺 */
.timeline-ruler-row {
  display: flex;
  height: 30px;
  border-bottom: 1px solid #3a3a3a;
}

.ruler-header {
  width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2a2a2a;
  border-right: 1px solid #3a3a3a;
  font-size: 12px;
  font-weight: 500;
  color: #cccccc;
}

.ruler-content {
  flex: 1;
  position: relative;
  background-color: #2a2a2a;
}

.time-mark {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #cccccc;
  padding-left: 4px;
  border-left: 1px solid #3a3a3a;
}

/* 播放头 */
.playhead {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #ff4444;
  cursor: ew-resize;
  z-index: 100;
}

.playhead::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  width: 14px;
  height: 14px;
  background-color: #ff4444;
  border-radius: 50%;
}

/* 轨道样式 */
.track {
  display: flex;
  height: 60px;
  /* 固定高度，所有片段都在同一条线上 */
  border-bottom: 1px solid #3a3a3a;
}

.track-header {
  width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  background-color: #2a2a2a;
  border-right: 1px solid #3a3a3a;
  font-size: 14px;
  font-weight: 500;
  position: relative;
}

.delete-track-btn {
  width: 18px;
  height: 18px;
  border: none;
  border-radius: 50%;
  background-color: #ff4444;
  color: white;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-content {
  flex: 1;
  position: relative;
  background-color: #1a1a1a;
  height: 100%;
  /* 填满轨道高度 */
}

/* 片段样式 */
.clip {
  position: absolute;
  top: 8px !important;
  /* 强制所有片段在同一水平线上 */
  height: 44px;
  border-radius: 4px;
  cursor: move;
  display: flex;
  align-items: center;
  padding: 0 8px;
  min-width: 50px;
  border: 1px solid transparent;
  transition: border-color 0.2s;
  overflow: hidden;
}

.clip:hover {
  border-color: #ffffff;
}

.clip.selected {
  border-color: #ffa500;
  box-shadow: 0 0 8px rgba(255, 165, 0, 0.5);
  background-color: rgba(255, 165, 0, 0.1);
}

.text-clip {
  background-color: #2e1e4e;
  border-color: #8b5cf6;
  color: #ffffff;
}

.text-clip:hover {
  border-color: #a855f7;
}

.text-clip .clip-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
}

.clip-simple {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.clip-simple .clip-name {
  color: white;
  font-size: 12px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.selected-clip-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background-color: rgba(255, 165, 0, 0.1);
  border: 1px solid #ffa500;
  border-radius: 4px;
  font-size: 12px;
  color: #ffa500;
}

.btn-clear {
  background: none;
  border: none;
  color: #ffa500;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.btn-clear:hover {
  background-color: rgba(255, 165, 0, 0.2);
}

.freeze-duration-control {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  padding: 2px 6px;
  background-color: rgba(255, 107, 107, 0.1);
  border: 1px solid #ff6b6b;
  border-radius: 3px;
}

.freeze-duration-control label {
  font-size: 11px;
  color: #ff6b6b;
  font-weight: 500;
}

.duration-input {
  width: 50px;
  padding: 2px 4px;
  border: 1px solid #3a3a3a;
  border-radius: 2px;
  background-color: #2a2a2a;
  color: #ffffff;
  font-size: 11px;
  text-align: center;
}

.duration-input:focus {
  outline: none;
  border-color: #ff6b6b;
}

.freeze-duration-control span {
  font-size: 11px;
  color: #cccccc;
}

.delete-clip-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 50%;
  background-color: #ff4444;
  color: white;
  font-size: 10px;
  cursor: pointer;
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.clip:hover .delete-clip-btn {
  display: flex;
}

/* 调整大小手柄 */
.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 8px;
  cursor: ew-resize;
  background-color: transparent;
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.resize-left {
  left: 0;
  border-left: 2px solid transparent;
}

.resize-right {
  right: 0;
  border-right: 2px solid transparent;
}

.clip:hover .resize-left {
  border-left-color: #ffffff;
}

.clip:hover .resize-right {
  border-right-color: #ffffff;
}

.video-clip {
  background-color: #007acc;
  background-image: linear-gradient(135deg, #007acc 0%, #0066aa 100%);
}

.video-clip.freeze-frame {
  background-color: #ff6b6b;
  background-image: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: 2px dashed #ffffff;
}

.video-clip.freeze-frame::before {
  content: '📸';
  position: absolute;
  top: 2px;
  right: 4px;
  font-size: 12px;
  z-index: 1;
}

.audio-clip {
  background-color: #6a4c93;
  background-image: linear-gradient(135deg, #6a4c93 0%, #5a3c83 100%);
}

.clip-content {
  flex: 1;
  min-width: 0;
}

.clip-name {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #ffffff;
}

/* 添加轨道按钮 */
.add-track-section {
  padding: 12px 16px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  display: flex;
  justify-content: center;
}

/* 拖拽提示 */
.track-content {
  position: relative;
}

.track-content.drag-over {
  background-color: rgba(0, 122, 204, 0.1);
  border: 2px dashed #007acc;
}

/* 素材拖拽样式 */
.asset-item[draggable='true'] {
  cursor: grab;
}

.asset-item[draggable='true']:active {
  cursor: grabbing;
}

/* 滚动条样式 */
.timeline-container::-webkit-scrollbar {
  height: 8px;
}

.timeline-container::-webkit-scrollbar-track {
  background-color: #2a2a2a;
}

.timeline-container::-webkit-scrollbar-thumb {
  background-color: #4a4a4a;
  border-radius: 4px;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background-color: #5a5a5a;
}

.assets-list::-webkit-scrollbar {
  width: 8px;
}

.assets-list::-webkit-scrollbar-track {
  background-color: #2a2a2a;
}

.assets-list::-webkit-scrollbar-thumb {
  background-color: #4a4a4a;
  border-radius: 4px;
}

.assets-list::-webkit-scrollbar-thumb:hover {
  background-color: #5a5a5a;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
  }

  .preview-panel {
    border-right: none;
    border-bottom: 1px solid #3a3a3a;
  }

  .assets-panel {
    border-left: none;
    border-top: 1px solid #3a3a3a;
    min-width: auto;
    height: 200px;
  }

  .timeline-panel {
    height: 150px;
  }

  /* 中等屏幕的时间轴标题栏调整 */
  .timeline-center-section {
    gap: 12px;
  }

  .time-display {
    font-size: 13px;
    min-width: 110px;
  }
}

@media (max-width: 768px) {
  .toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }

  .toolbar-section {
    flex-wrap: wrap;
  }

  .btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .track-header,
  .ruler-header {
    width: 80px;
    font-size: 12px;
  }

  /* 时间轴标题栏响应式 */
  .timeline-header {
    flex-direction: column;
    gap: 8px;
    min-height: auto;
    padding: 8px 12px;
  }

  .timeline-left-section h3 {
    font-size: 14px;
  }

  .timeline-center-section {
    flex-direction: column;
    gap: 8px;
  }

  .playback-controls {
    gap: 4px;
  }

  .time-display {
    font-size: 12px;
    min-width: 100px;
    padding: 4px 8px;
  }

  .timeline-right-section {
    align-self: stretch;
  }

  .timeline-controls {
    gap: 4px;
    justify-content: center;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 24px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.modal-content h3 {
  margin: 0 0 20px 0;
  color: #ffffff;
  font-size: 18px;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #cccccc;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  background-color: #1a1a1a;
  border: 1px solid #444444;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.form-group input[type='range'] {
  width: calc(100% - 50px);
  margin-right: 8px;
}

.form-group span {
  color: #cccccc;
  font-size: 12px;
  min-width: 40px;
  display: inline-block;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #444444;
}

.btn-secondary {
  background-color: #555555;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background-color: #666666;
}

/* 导出进度模态框样式 */
.export-progress-modal {
  min-width: 500px;
  max-width: 600px;
}

.progress-container {
  margin: 20px 0;
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #3a3a3a;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007acc 0%, #00c851 50%, #28a745 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.2) 50%,
      transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
}

.progress-status {
  text-align: center;
  margin: 15px 0;
  font-size: 16px;
  color: #cccccc;
  font-weight: 500;
}

.export-details {
  background-color: #1a1a1a;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
  border: 1px solid #3a3a3a;
}

.export-details p {
  margin: 8px 0;
  color: #cccccc;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.export-details p::before {
  content: '•';
  color: #007acc;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .export-progress-modal {
    min-width: 90%;
    max-width: 95%;
  }

  .progress-text {
    font-size: 12px;
  }

  .progress-status {
    font-size: 14px;
  }

  .export-details {
    padding: 10px;
  }

  .export-details p {
    font-size: 12px;
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
