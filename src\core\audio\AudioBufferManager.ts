/**
 * 音频缓冲管理器 - 解决音频预加载和缓冲问题
 */
export interface AudioClipInfo {
  id: string
  url: string
  startTime: number
  duration: number
  trimStart: number
  trimEnd: number
  volume: number
  trackId?: string
}

export interface AudioBufferData {
  buffer: AudioBuffer
  url: string
  duration: number
  loadTime: number
  isReady: boolean
}

export class AudioBufferManager {
  private audioContext: AudioContext
  private bufferCache: Map<string, AudioBufferData> = new Map()
  private loadingPromises: Map<string, Promise<AudioBufferData>> = new Map()
  private preloadQueue: Set<string> = new Set()
  private maxCacheSize = 50 // 最大缓存数量
  private preloadDistance = 10 // 预加载距离（秒）

  constructor(audioContext?: AudioContext) {
    this.audioContext = audioContext || new AudioContext()
  }

  /**
   * 预加载音频片段
   */
  public async preloadAudioClip(clip: AudioClipInfo): Promise<AudioBufferData> {
    const cacheKey = this.getCacheKey(clip.url)

    // 检查是否已经缓存
    const cached = this.bufferCache.get(cacheKey)
    if (cached && cached.isReady) {
      return cached
    }

    // 检查是否正在加载
    const loadingPromise = this.loadingPromises.get(cacheKey)
    if (loadingPromise) {
      return await loadingPromise
    }

    // 开始加载
    const promise = this.loadAudioBuffer(clip.url)
    this.loadingPromises.set(cacheKey, promise)

    try {
      const bufferData = await promise
      this.bufferCache.set(cacheKey, bufferData)
      this.loadingPromises.delete(cacheKey)

      // 清理缓存如果超过限制
      this.cleanupCache()

      return bufferData
    } catch (error) {
      this.loadingPromises.delete(cacheKey)

      throw error
    }
  }

  /**
   * 批量预加载音频片段
   */
  public async preloadMultipleClips(clips: AudioClipInfo[]): Promise<AudioBufferData[]> {
    const promises = clips.map((clip) => this.preloadAudioClip(clip))

    try {
      const results = await Promise.allSettled(promises)
      const successful: AudioBufferData[] = []
      const failed: string[] = []

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successful.push(result.value)
        } else {
          failed.push(clips[index].id)
        }
      })

      return successful
    } catch (error) {
      throw error
    }
  }

  /**
   * 智能预加载 - 根据当前时间预加载附近的音频
   */
  public async smartPreload(clips: AudioClipInfo[], currentTime: number): Promise<void> {
    const nearbyClips = clips.filter((clip) => {
      const clipStart = clip.startTime
      const clipEnd = clip.startTime + clip.duration
      const preloadStart = currentTime - this.preloadDistance
      const preloadEnd = currentTime + this.preloadDistance * 2

      return clipStart <= preloadEnd && clipEnd >= preloadStart
    })

    if (nearbyClips.length > 0) {
      await this.preloadMultipleClips(nearbyClips)
    }
  }

  /**
   * 获取缓存的音频缓冲区
   */
  public getCachedBuffer(url: string): AudioBufferData | null {
    const cacheKey = this.getCacheKey(url)
    const cached = this.bufferCache.get(cacheKey)
    return cached && cached.isReady ? cached : null
  }

  /**
   * 检查音频是否已缓存
   */
  public isAudioCached(url: string): boolean {
    const cached = this.getCachedBuffer(url)
    return cached !== null
  }

  /**
   * 创建音频源节点
   */
  public createAudioSource(url: string, startTime: number = 0): AudioBufferSourceNode | null {
    const bufferData = this.getCachedBuffer(url)
    if (!bufferData) {
      return null
    }

    try {
      const source = this.audioContext.createBufferSource()
      source.buffer = bufferData.buffer

      // 设置播放参数
      if (startTime > 0 && startTime < bufferData.duration) {
        source.start(0, startTime)
      } else {
        source.start(0)
      }

      return source
    } catch (error) {
      return null
    }
  }

  /**
   * 加载音频缓冲区
   */
  private async loadAudioBuffer(url: string): Promise<AudioBufferData> {
    const startTime = performance.now()

    try {
      // 获取音频数据
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()

      // 解码音频数据
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)

      const loadTime = performance.now() - startTime

      const bufferData: AudioBufferData = {
        buffer: audioBuffer,
        url: url,
        duration: audioBuffer.duration,
        loadTime: loadTime,
        isReady: true,
      }

      return bufferData
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(url: string): string {
    return url
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    if (this.bufferCache.size <= this.maxCacheSize) {
      return
    }

    // 按加载时间排序，删除最旧的缓存
    const entries = Array.from(this.bufferCache.entries())
    entries.sort((a, b) => a[1].loadTime - b[1].loadTime)

    // 计算需要删除的数量
    const excessCount = this.bufferCache.size - this.maxCacheSize
    const toDelete = entries.slice(0, excessCount)

    console.log(`🗑️ [CACHE] Deleting ${toDelete.length} cache entries`)

    toDelete.forEach(([key]) => {
      console.log(`🗑️ [CACHE] Deleting cache: ${key}`)
      this.bufferCache.delete(key)
    })
  }

  /**
   * 清空所有缓存
   */
  public clearCache(): void {
    console.log(`🗑️ [CACHE] Clearing all cache entries`)
    this.bufferCache.clear()
    this.loadingPromises.clear()
    this.preloadQueue.clear()
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): {
    totalCached: number
    totalSize: number
    totalDuration: number
    loading: number
  } {
    let totalSize = 0
    let totalDuration = 0

    this.bufferCache.forEach((bufferData) => {
      if (bufferData.buffer) {
        totalSize += bufferData.buffer.length * bufferData.buffer.numberOfChannels * 4 // 假设32位浮点
        totalDuration += bufferData.duration
      }
    })

    return {
      totalCached: this.bufferCache.size,
      totalSize: totalSize,
      totalDuration: totalDuration,
      loading: this.loadingPromises.size,
    }
  }

  /**
   * 销毁缓冲管理器
   */
  public dispose(): void {
    this.clearCache()
  }
}
