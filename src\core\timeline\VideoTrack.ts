/**
 * 视频轨道管理器
 * 管理视频片段的时间轴和播放逻辑
 */

export interface VideoTransform {
  x: number // X轴位置 (0-1, 相对于画布宽度)
  y: number // Y轴位置 (0-1, 相对于画布高度)
  scaleX: number // X轴缩放 (1.0 = 原始大小)
  scaleY: number // Y轴缩放 (1.0 = 原始大小)
  rotation: number // 旋转角度 (弧度)
}

export interface VideoClip {
  id: string
  videoElement: HTMLVideoElement
  name?: string // 片段名称（可选）
  startTime: number // 在时间轴上的开始时间（秒）
  duration: number // 片段持续时间（秒）
  trimStart: number // 视频内部裁剪开始时间（秒）
  trimEnd: number // 视频内部裁剪结束时间（秒）
  opacity: number // 透明度 0-1
  volume: number // 音量 0-1
  muted: boolean // 是否静音
  transform: VideoTransform // 视频变换
  effects: VideoEffect[] // 应用的效果
}

export interface VideoEffect {
  id: string
  type: 'blur' | 'brightness' | 'contrast' | 'saturation' | 'hue' | 'sepia' | 'grayscale'
  intensity: number // 效果强度 0-1
  enabled: boolean
}

export interface TrackState {
  currentTime: number
  isPlaying: boolean
  playbackRate: number
}

export class VideoTrack {
  private clips: VideoClip[] = []
  private currentTime: number = 0
  private isPlaying: boolean = false
  private playbackRate: number = 1.0
  private onTimeUpdateCallback?: (time: number) => void
  private animationFrameId?: number
  private pendingSeekClipIds: Set<string> = new Set()
  private trackId?: string // 添加轨道ID用于识别轨道二
  private isStable: boolean = false // 标记轨道是否处于稳定状态

  /**
   * 首帧热身：遍历所有clip，seek到首帧并drawImage一次，确保解码缓存
   */
  public warmUpAllClips(): void {
    this.clips.forEach((clip) => {
      const video = clip.videoElement
      // 只对普通视频片段热身
      if ((video as any)._isFreezeFrame) return
      // 跳到首帧
      video.currentTime = clip.trimStart
      // 尝试drawImage一次（如果可用）
      if (video.readyState >= video.HAVE_CURRENT_DATA) {
        try {
          const canvas = document.createElement('canvas')
          canvas.width = video.videoWidth || 100
          canvas.height = video.videoHeight || 100
          const ctx = canvas.getContext('2d')
          if (ctx) ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        } catch {}
      }
    })
  }

  constructor(trackId?: string) {
    this.trackId = trackId
    this.warmUpAllClips()
    this.startTimeUpdate()
  }

  /**
   * 设置轨道为稳定状态
   */
  public setStable(stable: boolean): void {
    this.isStable = stable
  }

  /**
   * 检查轨道是否稳定
   */
  public getIsStable(): boolean {
    return this.isStable
  }

  /**
   * 添加视频片段到轨道
   */
  public addClip(clip: VideoClip): void {
    // 按开始时间排序插入
    const insertIndex = this.clips.findIndex((c) => c.startTime > clip.startTime)
    if (insertIndex === -1) {
      this.clips.push(clip)
    } else {
      this.clips.splice(insertIndex, 0, clip)
    }

    // 设置视频元素的事件监听
    this.setupVideoElement(clip.videoElement)
  }

  /**
   * 移除视频片段
   */
  public removeClip(clipId: string): boolean {
    const index = this.clips.findIndex((c) => c.id === clipId)
    if (index !== -1) {
      this.clips.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * 获取指定时间点应该播放的视频片段
   */
  public getActiveClips(time: number): VideoClip[] {
    // 首先找到所有可能的候选片段
    const candidates = this.clips.filter((clip) => {
      const clipEndTime = clip.startTime + clip.duration
      const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame

      if (isFreezeFrame) {
        // 定格帧使用严格的边界处理，避免显示错误的帧
        return time >= clip.startTime && time < clipEndTime
      } else {
        // 普通视频使用精确边界
        const startTolerance = 0.01 // 开始时间容差10ms
        const endTolerance = 0.001 // 结束时间容差1ms，更严格

        const isInRange =
          time >= clip.startTime - startTolerance && time < clipEndTime + endTolerance

        return isInRange
      }
    })

    // 如果有多个候选片段（边界重叠情况），选择最合适的
    if (candidates.length > 1) {
      // 在边界处，优先选择即将开始的片段而不是即将结束的片段
      return candidates
        .filter((clip) => {
          const clipEndTime = clip.startTime + clip.duration
          const distanceFromStart = Math.abs(time - clip.startTime)
          const distanceFromEnd = Math.abs(time - clipEndTime)

          // 如果距离开始时间更近，优先选择这个片段
          return distanceFromStart <= distanceFromEnd
        })
        .slice(0, 1) // 只返回一个片段
    }

    return candidates
  }

  /**
   * 获取所有视频片段
   */
  public getAllClips(): VideoClip[] {
    return [...this.clips]
  }

  /**
   * 根据ID查找片段
   */
  public findClipById(id: string): VideoClip | undefined {
    return this.clips.find((clip) => clip.id === id)
  }

  /**
   * 设置当前播放时间
   */
  public setCurrentTime(time: number): void {
    this.currentTime = Math.max(0, time)
    this.updateVideoElements()
  }

  /**
   * 获取当前播放时间
   */
  public getCurrentTime(): number {
    return this.currentTime
  }

  /**
   * 开始播放
   */
  public play(): void {
    if (!this.isPlaying) {
      this.isPlaying = true
      this.updateVideoElements()
    }
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    if (this.isPlaying) {
      this.isPlaying = false
      this.updateVideoElements()
    }
  }

  /**
   * 设置播放速率
   */
  public setPlaybackRate(rate: number): void {
    this.playbackRate = Math.max(0.1, Math.min(4.0, rate))
    this.updateVideoElements()
  }

  /**
   * 获取轨道总时长
   */
  public getTotalDuration(): number {
    if (this.clips.length === 0) return 0

    return Math.max(...this.clips.map((clip) => clip.startTime + clip.duration))
  }

  /**
   * 更新视频元素的播放状态 - 为轨道二添加温和的缓存保护
   */
  private updateVideoElements(): void {
    // 先获取所有理论上活跃的片段
    const allCandidates = this.getActiveClips(this.currentTime)
    const activeClips: VideoClip[] = []
    allCandidates.forEach((clip) => {
      const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame
      if (isFreezeFrame) {
        activeClips.push(clip)
        return
      }
      const relativeTime = this.currentTime - clip.startTime
      const videoTime = clip.trimStart + relativeTime
      const maxVideoTime = clip.trimEnd || clip.trimStart + clip.duration
      const clampedVideoTime = Math.max(clip.trimStart, Math.min(videoTime, maxVideoTime))
      const timeDiff = Math.abs(clip.videoElement.currentTime - clampedVideoTime)

      // 对URL视频和克隆视频使用更宽松的时间同步阈值
      const isUrlVideo = clip.videoElement.src.startsWith('http')
      const isClonedVideo = (clip.videoElement as any)._isClonedVideo

      // 轨道二强化保护：使用更大的阈值和更智能的时间检查
      const isTrack2 = this.trackId === 'track-2'
      const baseThreshold =
        isUrlVideo || isClonedVideo
          ? this.isPlaying
            ? 0.5
            : 0.3 // URL视频和克隆视频使用更大的阈值
          : this.isPlaying
            ? 0.3
            : 0.2 // 本地视频保持原有阈值

      // 轨道二使用更大的阈值，并且在稳定状态下进一步放宽
      let threshold = baseThreshold
      if (isTrack2) {
        // 轨道二基础阈值放大3倍
        threshold = baseThreshold * 3

        // 如果轨道二处于稳定状态，进一步放宽阈值
        if (this.isStable) {
          threshold = baseThreshold * 5 // 稳定状态下放大5倍
        }

        // 检查视频是否已经在合理范围内播放
        const videoCurrentTime = clip.videoElement.currentTime
        const isVideoPlaying =
          !clip.videoElement.paused &&
          clip.videoElement.readyState >= clip.videoElement.HAVE_CURRENT_DATA

        // 如果视频正在正常播放且时间差不是很大，跳过时间调整
        if (isVideoPlaying && timeDiff < 1.0) {
          activeClips.push(clip)
          return
        }
      }

      // 检查克隆视频是否刚刚同步过，避免频繁调整
      if (isClonedVideo) {
        const lastSyncTime = (clip.videoElement as any)._lastSyncTime || 0
        const timeSinceSync = Date.now() - lastSyncTime

        // 如果刚刚同步过（500ms内），使用更大的阈值
        if (timeSinceSync < 500) {
          const relaxedThreshold = Math.max(threshold, 1.0)
          if (timeDiff <= relaxedThreshold) {
            // 跳过这次调整，避免频繁的时间设置
            activeClips.push(clip)
            return
          }
        }
      }

      // 如果时间差大于阈值，且未在pending中，则seek并等待
      if (timeDiff > threshold) {
        if (!this.pendingSeekClipIds.has(clip.id)) {
          this.pendingSeekClipIds.add(clip.id)

          // 保存当前要设置的时间，避免延迟期间时间变化
          const targetTime = clampedVideoTime

          const onSeeked = () => {
            this.pendingSeekClipIds.delete(clip.id)
            clip.videoElement.removeEventListener('seeked', onSeeked)
            // 不再递归调用updateVideoElements，避免时间计算错乱
          }
          clip.videoElement.addEventListener('seeked', onSeeked)

          // 轨道二温和保护：减少seek操作的延迟
          if (isTrack2) {
            // 轨道二直接设置时间，减少延迟
            clip.videoElement.currentTime = targetTime
          } else if (isClonedVideo) {
            // 克隆视频直接设置时间，不使用延迟
            clip.videoElement.currentTime = targetTime
          } else if (isUrlVideo) {
            // URL视频保持少量延迟
            setTimeout(() => {
              clip.videoElement.currentTime = targetTime
            }, 20)
          } else {
            clip.videoElement.currentTime = targetTime
          }
        }
        // seek中，不加入活跃片段
        return
      }
      // 放宽准备状态要求，减少拼接点闪烁
      // 对于URL视频和克隆视频，允许更低的准备状态进行渲染
      const minReadyState =
        isUrlVideo || isClonedVideo
          ? clip.videoElement.HAVE_CURRENT_DATA // URL视频和克隆视频允许更低的准备状态
          : clip.videoElement.HAVE_ENOUGH_DATA // 本地视频保持原有要求

      if (clip.videoElement.readyState < minReadyState) {
        return
      }
      // seek已完成，允许活跃
      this.pendingSeekClipIds.delete(clip.id)
      activeClips.push(clip)
    })

    // 暂停所有非活跃的视频
    this.clips.forEach((clip) => {
      if (!activeClips.includes(clip)) {
        if (!clip.videoElement.paused) {
          clip.videoElement.pause()
        }
      }
    })

    // 预加载接下来的视频片段
    this.prepareUpcomingClips(this.currentTime + 2.0)

    // 更新活跃视频的播放状态
    activeClips.forEach((clip) => {
      const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame
      if (isFreezeFrame) {
        clip.videoElement.muted = true
        clip.videoElement.volume = 0
        if (!clip.videoElement.paused) {
          clip.videoElement.pause()
        }
        return
      }
      // 设置播放速率
      clip.videoElement.playbackRate = this.playbackRate
      // 保持视频元素静音，但音量设为1让AudioMixer能获取音频数据
      clip.videoElement.volume = 1.0
      clip.videoElement.muted = true
      // 控制播放/暂停
      if (this.isPlaying) {
        if (clip.videoElement.paused) {
          clip.videoElement.play().catch(() => {})
        }
      } else {
        if (!clip.videoElement.paused) {
          clip.videoElement.pause()
        }
      }
    })
  }

  /**
   * 简单有效的预加载：专注于数据缓冲而不是复杂的时间控制
   */
  private prepareUpcomingClips(futureTime: number): void {
    // 预加载接下来的2个视频片段
    const upcomingClips = this.clips
      .filter((clip) => {
        const timeTillStart = clip.startTime - this.currentTime
        return timeTillStart > 0 && timeTillStart <= 2.0
      })
      .slice(0, 2) // 只预加载接下来的2个片段

    upcomingClips.forEach((clip) => {
      const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame
      const isCloned = (clip.videoElement as any)._isCloned

      if (isFreezeFrame) {
        return // 定格帧不需要预加载
      }

      // 强化切割后片段的预加载
      if (isCloned) {
        // 切割后的片段需要更积极的预加载
        clip.videoElement.preload = 'auto'
        // 如果数据不足，强制加载
        if (clip.videoElement.readyState < clip.videoElement.HAVE_ENOUGH_DATA) {
          clip.videoElement.load()
          setTimeout(() => {
            if (clip.videoElement.readyState < clip.videoElement.HAVE_CURRENT_DATA) {
              clip.videoElement.load()
            }
          }, 200)
        }
      } else {
        // 普通片段的预加载
        if (clip.videoElement.readyState < clip.videoElement.HAVE_ENOUGH_DATA) {
          clip.videoElement.preload = 'auto'
          clip.videoElement.load()
        }
      }

      // 只对非切割片段设置时间，避免切割片段的时间设置干扰
      if (!isCloned) {
        const targetTime = clip.trimStart
        if (Math.abs(clip.videoElement.currentTime - targetTime) > 0.5) {
          try {
            clip.videoElement.currentTime = targetTime
          } catch (error) {
            // 忽略时间设置错误
          }
        }
      }
    })
  }

  /**
   * 设置视频元素的基本配置
   */
  private setupVideoElement(video: HTMLVideoElement): void {
    video.preload = 'metadata'
    video.muted = true // 静音视频元素，避免直接播放音频
    video.playsInline = true

    // 禁用默认控件
    video.controls = false

    // 设置音量为1，但保持静音状态，让AudioMixer通过Web Audio API获取音频
    video.volume = 1.0

    // 设置crossOrigin确保AudioContext可以访问
    video.crossOrigin = 'anonymous'
  }

  /**
   * 开始时间更新循环
   */
  private startTimeUpdate(): void {
    const updateTime = () => {
      if (this.isPlaying) {
        // 简单的时间推进，实际应用中可能需要更精确的时间同步
        this.currentTime += (1 / 60) * this.playbackRate // 假设60fps

        // 不在这里检查播放结束，让VideoEditor来控制全局播放时长

        // 触发时间更新回调
        if (this.onTimeUpdateCallback) {
          this.onTimeUpdateCallback(this.currentTime)
        }
      }

      // 无论是否播放都要更新视频元素状态
      this.updateVideoElements()

      this.animationFrameId = requestAnimationFrame(updateTime)
    }

    updateTime()
  }

  /**
   * 设置时间更新回调
   */
  public onTimeUpdate(callback: (time: number) => void): void {
    this.onTimeUpdateCallback = callback
  }

  /**
   * 获取轨道状态
   */
  public getState(): TrackState {
    return {
      currentTime: this.currentTime,
      isPlaying: this.isPlaying,
      playbackRate: this.playbackRate,
    }
  }

  /**
   * 获取所有片段
   */
  public getClips(): VideoClip[] {
    return [...this.clips]
  }

  /**
   * 更新片段属性
   */
  public updateClip(clipId: string, updates: Partial<VideoClip>): boolean {
    const clip = this.clips.find((c) => c.id === clipId)
    if (clip) {
      Object.assign(clip, updates)
      this.updateVideoElements()
      return true
    }
    return false
  }

  /**
   * 销毁轨道
   */
  public destroy(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
    }

    // 暂停所有视频
    this.clips.forEach((clip) => {
      clip.videoElement.pause()
    })

    this.clips = []
    this.isPlaying = false
  }
}
