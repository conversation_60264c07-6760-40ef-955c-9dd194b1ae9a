/**
 * 视频编辑器主引擎
 * 整合WebGL渲染、音频混合、时间轴管理等功能
 */

import { VideoRenderer } from './webgl/VideoRenderer'
import { VideoTrack, type VideoClip } from './timeline/VideoTrack'
import { TextTrack, type TextClip } from './timeline/TextTrack'
import { AudioMixer, type AudioClip } from './audio/AudioMixer'

export interface VideoEditorOptions {
  canvas: HTMLCanvasElement
  width: number
  height: number
  frameRate?: number
}

export interface EditorState {
  currentTime: number
  isPlaying: boolean
  playbackRate: number
  duration: number
  volume: number
}

export class VideoEditor {
  private renderer: VideoRenderer
  private videoTracks: Map<string, VideoTrack> = new Map()
  private textTrack: TextTrack = new TextTrack()
  private audioMixer: AudioMixer
  private canvas: HTMLCanvasElement
  private width: number
  private height: number
  private frameRate: number
  private animationFrameId?: number
  private recordingStream?: MediaStream
  private mediaRecorder?: MediaRecorder
  private recordedChunks: Blob[] = []
  private _lastFrameHadContent: boolean = false // 保留变量但移除调试用途

  // 事件回调
  private onStateChangeCallback?: (state: EditorState) => void
  private onTimeUpdateCallback?: (time: number) => void

  constructor(options: VideoEditorOptions) {
    this.canvas = options.canvas
    this.width = options.width
    this.height = options.height
    this.frameRate = options.frameRate || 30

    // 初始化渲染器
    this.renderer = new VideoRenderer({
      canvas: this.canvas,
      width: this.width,
      height: this.height,
    })

    // 初始化视频轨道
    this.videoTracks.set('track-1', new VideoTrack('track-1'))
    this.videoTracks.set('track-2', new VideoTrack('track-2'))

    // 初始化音频混合器
    this.audioMixer = new AudioMixer()

    // 设置统一的时间更新系统
    this.setupTimeSync()

    // 开始渲染循环
    this.startRenderLoop()

    // 启动全局预加载策略
    this.startGlobalPreloading()
  }

  /**
   * 设置时间同步系统（支持动态轨道选择）
   */
  private setupTimeSync(): void {
    let lastUpdateTime = 0
    const updateThreshold = 16 // 约60FPS的更新频率

    // 为所有轨道设置时间更新回调
    this.videoTracks.forEach((track, trackId) => {
      track.onTimeUpdate((time) => {
        // 只有当该轨道正在播放时才处理时间更新
        if (!track.getState().isPlaying) {
          return
        }

        const now = Date.now()
        if (now - lastUpdateTime < updateThreshold) {
          return // 限制更新频率
        }
        lastUpdateTime = now

        // 检查是否到达项目总时长（只有当总时长大于0时才检查）
        const totalDuration = this.getTotalDuration()
        if (totalDuration > 0 && time >= totalDuration) {
          // 到达项目末尾，暂停所有轨道
          this.videoTracks.forEach((t) => t.pause())
          return
        }

        // 同步其他轨道的时间（不触发它们的回调）
        this.videoTracks.forEach((otherTrack, otherTrackId) => {
          if (otherTrackId !== trackId) {
            otherTrack.setCurrentTime(time)
          }
        })

        this.updateAudioTime(time)
        // 不在这里调用renderFrame，让渲染循环处理

        if (this.onTimeUpdateCallback) {
          this.onTimeUpdateCallback(time)
        }

        this.notifyStateChange()
      })
    })
  }

  /**
   * 添加视频片段
   */
  public async addVideoClip(
    videoFile: File,
    startTime: number = 0,
    trackId: string = 'track-1',
  ): Promise<string> {
    const videoElement = document.createElement('video')
    const objectUrl = URL.createObjectURL(videoFile)
    videoElement.src = objectUrl
    // 移除crossOrigin设置，因为本地文件不需要CORS
    videoElement.preload = 'metadata'
    videoElement.muted = true // 静音视频元素，避免直接播放音频
    videoElement.volume = 1.0 // 设置音量为1，让AudioMixer能获取音频数据

    return new Promise((resolve, reject) => {
      // 设置超时处理
      const timeoutId = setTimeout(() => {
        URL.revokeObjectURL(objectUrl)
        reject(new Error('Video loading timeout'))
      }, 10000) // 10秒超时

      videoElement.onloadedmetadata = () => {
        clearTimeout(timeoutId)

        try {
          const clipId = this.generateId()
          const duration = videoElement.duration

          // 验证视频时长
          if (!duration || duration <= 0 || !isFinite(duration)) {
            URL.revokeObjectURL(objectUrl)
            reject(new Error('Invalid video duration'))
            return
          }

          const videoClip: VideoClip = {
            id: clipId,
            videoElement,
            name: videoFile.name, // 保存原始文件名
            startTime,
            duration,
            trimStart: 0,
            trimEnd: duration,
            opacity: 1.0,
            volume: 1.0,
            muted: false,
            transform: {
              x: 0.5, // 居中
              y: 0.5, // 居中
              scaleX: 1.0, // 原始大小
              scaleY: 1.0, // 原始大小
              rotation: 0, // 无旋转
            },
            effects: [],
          }

          const track = this.videoTracks.get(trackId)
          if (!track) {
            throw new Error(`Track ${trackId} not found`)
          }
          track.addClip(videoClip)

          // 如果是轨道二，添加视频加载监听器以检测稳定性
          if (trackId === 'track-2') {
            const onCanPlay = () => {
              videoElement.removeEventListener('canplay', onCanPlay)
              // 延迟检查稳定性，确保所有片段都有机会加载
              setTimeout(() => {
                this.isTrack2Stable()
              }, 1500)
            }

            // 如果视频已经可以播放，立即触发检查
            if (videoElement.readyState >= videoElement.HAVE_CURRENT_DATA) {
              setTimeout(() => {
                this.isTrack2Stable()
              }, 1500)
            } else {
              videoElement.addEventListener('canplay', onCanPlay)
            }
          }

          // 如果视频有音频轨道，也添加到音频混合器
          if (this.hasAudioTrack(videoElement)) {
            try {
              const audioElement = new Audio()
              // 恢复crossOrigin设置，保持与视频元素的一致性
              audioElement.crossOrigin = 'anonymous'
              audioElement.src = objectUrl
              audioElement.preload = 'metadata'

              const audioClip: AudioClip = {
                id: clipId + '_audio',
                audioElement,
                startTime,
                duration,
                trimStart: 0,
                trimEnd: duration,
                volume: 1.0,
                muted: false,
                fadeIn: 0,
                fadeOut: 0,
                effects: [],
                trackId: trackId, // 设置轨道ID，用于录制时过滤
              }

              this.audioMixer.addClip(audioClip)
            } catch (error) {
              // Continue without audio if adding fails
            }
          }

          // 自动跳转到新添加的片段开始时间
          this.setCurrentTime(startTime)

          this.notifyStateChange()
          resolve(clipId)
        } catch (error) {
          URL.revokeObjectURL(objectUrl)
          reject(error)
        }
      }

      videoElement.onerror = () => {
        clearTimeout(timeoutId)
        URL.revokeObjectURL(objectUrl)
        reject(
          new Error(
            'Failed to load video file: ' + (videoElement.error?.message || 'Unknown error'),
          ),
        )
      }

      // 添加其他错误处理
      videoElement.onabort = () => {
        clearTimeout(timeoutId)
        URL.revokeObjectURL(objectUrl)
        reject(new Error('Video loading aborted'))
      }
    })
  }

  /**
   * 添加音频片段
   */
  public async addAudioClip(audioFile: File, startTime: number = 0): Promise<string> {
    const audioElement = new Audio()
    audioElement.src = URL.createObjectURL(audioFile)
    audioElement.crossOrigin = 'anonymous'
    audioElement.preload = 'metadata'

    return new Promise((resolve, reject) => {
      audioElement.onloadedmetadata = () => {
        const clipId = this.generateId()
        const duration = audioElement.duration

        const audioClip: AudioClip = {
          id: clipId,
          audioElement,
          startTime,
          duration,
          trimStart: 0,
          trimEnd: duration,
          volume: 1.0,
          muted: false,
          fadeIn: 0,
          fadeOut: 0,
          effects: [],
        }

        this.audioMixer.addClip(audioClip)
        this.notifyStateChange()
        resolve(clipId)
      }

      audioElement.onerror = () => {
        reject(new Error('Failed to load audio file'))
      }
    })
  }

  /**
   * 移除片段
   */
  public removeClip(clipId: string): boolean {
    let videoRemoved = false
    let removedClip: any = null

    // 从所有轨道中尝试移除，并获取被移除的片段
    this.videoTracks.forEach((track) => {
      const clip = track.findClipById(clipId)
      if (clip) {
        removedClip = clip
        if (track.removeClip(clipId)) {
          videoRemoved = true
        }
      }
    })

    // 如果移除的是定格帧，清理相关纹理
    if (removedClip && (removedClip.videoElement as any)._isFreezeFrame) {
      this.renderer.cleanupTexture(removedClip.videoElement)
    }

    const audioRemoved =
      this.audioMixer.removeClip(clipId) || this.audioMixer.removeClip(clipId + '_audio')

    if (videoRemoved || audioRemoved) {
      this.notifyStateChange()
      return true
    }
    return false
  }

  /**
   * 播放 - 用户点击播放按钮时执行
   */
  public async play(): Promise<void> {
    const currentTime = this.getCurrentTime()

    // 1. 确保AudioContext在用户交互后能正确启动
    try {
      await this.audioMixer.ensureAudioContextRunning()
    } catch (error) {}

    // 2. 如果音频还没准备好，先准备音频（但不播放）
    await this.ensureCurrentFrameAudioReady(currentTime)

    // 3. 准备视频轨道
    await this.prepareVideoTracks(currentTime)

    // 4. 最终音频验证
    await this.finalAudioSync(currentTime)

    // 5. 现在用户点击了播放，开始真正的播放
    await this.startActualPlayback(currentTime)

    this.notifyStateChange()
  }

  /**
   * 开始真正的播放（轨道2音频已经准备好）
   */
  private async startActualPlayback(time: number): Promise<void> {
    // 1. 启动轨道2音频播放（轨道1没有音频）
    const allActiveClips = this.audioMixer.getActiveClips(time)
    const track2AudioClips = allActiveClips.filter((clip) => {
      return clip.trackId === 'track-2' || clip.id.includes('track-2') || clip.id.endsWith('_audio')
    })

    const audioPlayPromises = track2AudioClips.map(async (clip) => {
      const audio = clip.audioElement
      try {
        if (audio.paused && audio.readyState >= audio.HAVE_CURRENT_DATA) {
          await audio.play()
        }
      } catch (error) {}
    })

    // 2. 启动视频播放
    let hasActiveTrack = false
    this.videoTracks.forEach((track) => {
      const activeVideoClips = track.getActiveClips(time)
      if (activeVideoClips.length > 0) {
        track.play()
        hasActiveTrack = true
      }
    })

    // 如果没有活跃轨道，播放所有轨道
    if (!hasActiveTrack) {
      this.videoTracks.forEach((track) => track.play())
    }

    // 3. 等待轨道2音频播放启动
    await Promise.all(audioPlayPromises)

    // 4. 短暂等待确保播放启动
    await new Promise((resolve) => setTimeout(resolve, 50))
  }

  /**
   * 检查当前音频状态
   */
  private async checkAudioState(time: number): Promise<void> {
    const activeClips = this.audioMixer.getActiveClips(time)

    for (const clip of activeClips) {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const expectedTime = clip.trimStart + relativeTime

      // 如果音频时间偏差太大，强制重新设置（录制期间跳过）
      if (!this.isRecording() && Math.abs(audio.currentTime - expectedTime) > 0.2) {
        try {
          audio.currentTime = expectedTime
          // 等待时间设置完成
          await new Promise((resolve) => {
            const onSeeked = () => {
              audio.removeEventListener('seeked', onSeeked)
              resolve(undefined)
            }
            audio.addEventListener('seeked', onSeeked, { once: true })
            setTimeout(() => {
              audio.removeEventListener('seeked', onSeeked)
              resolve(undefined)
            }, 300)
          })
        } catch (error) {}
      }
    }
  }

  /**
   * 准备视频轨道播放
   */
  private async prepareVideoTracks(time: number): Promise<void> {
    const trackPromises: Promise<void>[] = []

    this.videoTracks.forEach((track) => {
      const activeClips = track.getActiveClips(time)
      if (activeClips.length > 0) {
        // 为每个活跃片段准备播放
        const trackPromise = Promise.all(
          activeClips.map(async (clip) => {
            return new Promise<void>((resolve) => {
              const video = clip.videoElement

              // 如果视频已经准备好，直接resolve
              if (video.readyState >= video.HAVE_CURRENT_DATA) {
                resolve()
                return
              }

              // 等待视频准备完成
              const timeout = setTimeout(() => {
                resolve()
              }, 1000)

              const onCanPlay = () => {
                clearTimeout(timeout)
                video.removeEventListener('canplay', onCanPlay)
                resolve()
              }

              video.addEventListener('canplay', onCanPlay, { once: true })
            })
          }),
        ).then(() => {})

        trackPromises.push(trackPromise)
      }
    })

    await Promise.all(trackPromises)
  }

  /**
   * 同步启动音频和视频播放
   */
  private async synchronizedStart(time: number): Promise<void> {
    // 同时启动所有轨道的播放
    let hasActiveTrack = false

    this.videoTracks.forEach((track) => {
      const activeClips = track.getActiveClips(time)
      if (activeClips.length > 0) {
        track.play()
        hasActiveTrack = true
      }
    })

    // 如果没有活跃轨道，播放所有轨道
    if (!hasActiveTrack) {
      this.videoTracks.forEach((track) => track.play())
    }

    // 短暂等待确保播放启动
    await new Promise((resolve) => setTimeout(resolve, 50))
  }

  /**
   * 暂停
   */
  public pause(): void {
    this.videoTracks.forEach((track) => track.pause())

    // 强制暂停所有音频，确保没有音频继续播放
    this.audioMixer.pauseAll()

    // 更新音频状态为暂停状态
    this.updateAudioTime(this.getCurrentTime())

    this.notifyStateChange()
  }

  /**
   * 设置当前时间 - 增强音频同步机制，温和保护轨道二缓存
   */
  public async setCurrentTime(time: number): Promise<void> {
    // 1. 先暂停所有音频，避免音视频不同步
    this.audioMixer.pauseAll()
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 2. 强制音频同步准备 - 确保目标时间点音频完全加载
    await this.ensureCurrentFrameAudioReady(time)

    // 3. 设置视频时间 - 轨道2优先，但使用温和的保护策略
    const track2 = this.videoTracks.get('track-2')
    if (track2) {
      // 轨道2设置时间，但减少频繁调用
      const currentTime = track2.getCurrentTime()
      const timeDiff = Math.abs(currentTime - time)

      // 只有时间差异较大时才设置，减少对轨道二的干扰
      if (timeDiff > 0.1) {
        track2.setCurrentTime(time)
      }
    }

    // 4. 轨道1正常设置时间
    const track1 = this.videoTracks.get('track-1')
    if (track1) {
      track1.setCurrentTime(time)
    }

    // 5. 其他轨道（如果有）
    this.videoTracks.forEach((track, trackId) => {
      if (trackId !== 'track-1' && trackId !== 'track-2') {
        track.setCurrentTime(time)
      }
    })

    // 6. 等待视频时间设置完成
    await this.waitForVideoSync(time)

    // 7. 最终音频验证
    await this.finalAudioSync(time)

    // 8. 更新音频时间状态
    this.updateAudioTime(time)

    // 9. 最后渲染视频帧（此时音频和视频都已经准备好）
    this.renderFrame()

    this.notifyStateChange()
  }

  /**
   * 等待视频时间同步完成
   */
  private async waitForVideoSync(targetTime: number): Promise<void> {
    const syncPromises: Promise<void>[] = []

    this.videoTracks.forEach((track, trackId) => {
      const activeClips = track.getActiveClips(targetTime)
      activeClips.forEach((clip) => {
        const syncPromise = new Promise<void>((resolve) => {
          const video = clip.videoElement
          const relativeTime = targetTime - clip.startTime
          const expectedVideoTime = clip.trimStart + relativeTime

          // 检查视频时间是否已经同步
          const checkSync = () => {
            const timeDiff = Math.abs(video.currentTime - expectedVideoTime)
            if (timeDiff < 0.1 || video.readyState >= video.HAVE_CURRENT_DATA) {
              resolve()
            } else {
              // 继续等待
              setTimeout(checkSync, 50)
            }
          }

          // 设置超时避免无限等待
          setTimeout(() => {
            resolve()
          }, 1000)

          checkSync()
        })

        syncPromises.push(syncPromise)
      })
    })

    await Promise.all(syncPromises)
  }

  /**
   * 获取当前时间
   */
  public getCurrentTime(): number {
    // 优先使用有活跃片段的轨道时间
    for (const [trackId, track] of this.videoTracks) {
      const currentTime = track.getCurrentTime()
      const activeClips = track.getActiveClips(currentTime)
      if (activeClips.length > 0) {
        return currentTime
      }
    }

    // 如果没有活跃片段，使用主轨道时间
    const mainTrack = this.videoTracks.get('track-1')
    return mainTrack ? mainTrack.getCurrentTime() : 0
  }

  /**
   * 获取视频轨道（用于UI层访问）
   */
  public getVideoTrack(trackId: string = 'track-1'): VideoTrack | undefined {
    return this.videoTracks.get(trackId)
  }

  /**
   * 获取所有视频轨道
   */
  public getAllVideoTracks(): Map<string, VideoTrack> {
    return this.videoTracks
  }

  /**
   * 更新音频片段的开始时间（用于视频片段拖动时同步音频）
   */
  public updateAudioClipStartTime(videoClipId: string, newStartTime: number): boolean {
    // 音频片段的ID通常是视频片段ID + '_audio'
    const audioClipId = videoClipId + '_audio'
    return this.audioMixer.updateClipStartTime(audioClipId, newStartTime)
  }

  /**
   * 更新音频片段的裁剪时间（用于视频片段调整大小时同步音频）
   */
  public updateAudioClipTrimTimes(
    videoClipId: string,
    trimStart: number,
    trimEnd: number,
  ): boolean {
    // 音频片段的ID通常是视频片段ID + '_audio'
    const audioClipId = videoClipId + '_audio'
    return this.audioMixer.updateClipTrimTimes(audioClipId, trimStart, trimEnd)
  }

  /**
   * 获取音频混合器（用于UI层访问）
   */
  public getAudioMixer(): AudioMixer {
    return this.audioMixer
  }

  /**
   * 设置播放速率
   */
  public setPlaybackRate(rate: number): void {
    this.videoTracks.forEach((track) => track.setPlaybackRate(rate))
    this.notifyStateChange()
  }

  /**
   * 设置主音量
   */
  public setMasterVolume(volume: number): void {
    this.audioMixer.setMasterVolume(volume)
    this.notifyStateChange()
  }

  /**
   * 获取项目总时长
   */
  public getTotalDuration(): number {
    let maxDuration = 0

    // 检查视频轨道
    this.videoTracks.forEach((track) => {
      const trackDuration = track.getTotalDuration()
      if (trackDuration > maxDuration) {
        maxDuration = trackDuration
      }
    })

    // 检查文字轨道
    const textDuration = this.textTrack.getDuration()
    if (textDuration > maxDuration) {
      maxDuration = textDuration
    }

    return maxDuration
  }

  /**
   * 添加文字片段
   */
  public addTextClip(textClip: TextClip): void {
    this.textTrack.addClip(textClip)
  }

  /**
   * 移除文字片段
   */
  public removeTextClip(clipId: string): void {
    this.textTrack.removeClip(clipId)
  }

  /**
   * 更新文字片段
   */
  public updateTextClip(clipId: string, updates: Partial<TextClip>): void {
    this.textTrack.updateClip(clipId, updates)
  }

  /**
   * 获取所有文字片段
   */
  public getAllTextClips(): TextClip[] {
    return this.textTrack.getAllClips()
  }

  /**
   * 获取当前时间的活跃文字片段
   */
  public getActiveTextClips(): TextClip[] {
    return this.textTrack.getActiveClips(this.getCurrentTime())
  }

  /**
   * 检查音频是否完全准备好
   */
  private async checkAudioFullyReady(time: number): Promise<boolean> {
    console.log('🎬 [AUDIO-CHECK] Checking if audio is fully ready')

    const activeClips = this.audioMixer.getActiveClips(time)
    const track2AudioClips = activeClips.filter((clip) => {
      return clip.trackId === 'track-2' || clip.id.includes('track-2') || clip.id.endsWith('_audio')
    })

    if (track2AudioClips.length === 0) {
      console.log('🎬 [AUDIO-CHECK] No track-2 audio clips found, considering ready')
      return true
    }

    console.log(`🎬 [AUDIO-CHECK] Checking ${track2AudioClips.length} track-2 audio clips`)

    // 检查每个音频片段是否准备好
    for (let i = 0; i < track2AudioClips.length; i++) {
      const clip = track2AudioClips[i]
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      // 检查音频数据是否充分加载
      const hasEnoughData = audio.readyState >= audio.HAVE_CURRENT_DATA
      const hasCorrectTime = Math.abs(audio.currentTime - targetAudioTime) < 0.2
      const isNotPaused = !audio.paused
      const hasValidDuration = audio.duration > 0 && !isNaN(audio.duration)

      console.log(
        `🎬 [AUDIO-CHECK] Clip ${i + 1}/${track2AudioClips.length} (${clip.id}): readyState=${audio.readyState}, hasEnoughData=${hasEnoughData}, hasCorrectTime=${hasCorrectTime}, isNotPaused=${isNotPaused}, hasValidDuration=${hasValidDuration}`,
      )

      if (!hasEnoughData || !hasCorrectTime || !isNotPaused || !hasValidDuration) {
        // 尝试修复音频状态
        console.log(`🎬 [AUDIO-CHECK] 🔄 Attempting to fix clip ${i + 1}: ${clip.id}`)

        try {
          if (!hasEnoughData) {
            audio.load()
          }

          if (!hasCorrectTime) {
            audio.currentTime = targetAudioTime
          }

          if (audio.paused) {
            await audio.play()
          }

          // 短暂等待让修复生效
          await new Promise((resolve) => setTimeout(resolve, 200))

          // 重新检查
          const newHasEnoughData = audio.readyState >= audio.HAVE_CURRENT_DATA
          const newHasCorrectTime = Math.abs(audio.currentTime - targetAudioTime) < 0.2
          const newIsNotPaused = !audio.paused

          if (!newHasEnoughData || !newHasCorrectTime || !newIsNotPaused) {
            console.log(`🎬 [AUDIO-CHECK] ❌ Clip ${i + 1} still not ready after fix attempt`)
            return false
          } else {
            console.log(`🎬 [AUDIO-CHECK] ✅ Clip ${i + 1} fixed successfully`)
          }
        } catch (error) {
          console.warn(`🎬 [AUDIO-CHECK] ⚠️ Failed to fix clip ${i + 1}:`, error)
          return false
        }
      } else {
        console.log(`🎬 [AUDIO-CHECK] ✅ Clip ${i + 1} is ready`)
      }
    }

    console.log('🎬 [AUDIO-CHECK] ✅ All audio clips are ready')
    return true
  }

  /**
   * 确保音频连续性用于录制恢复
   */
  private async ensureAudioContinuityForRecording(time: number): Promise<void> {
    console.log('🎬 [AUDIO-CONTINUITY] Ensuring audio continuity for recording resume')

    const activeClips = this.audioMixer.getActiveClips(time)
    const track2AudioClips = activeClips.filter((clip) => {
      return clip.trackId === 'track-2' || clip.id.includes('track-2') || clip.id.endsWith('_audio')
    })

    if (track2AudioClips.length === 0) {
      console.log('🎬 [AUDIO-CONTINUITY] No track-2 audio clips found')
      return
    }

    console.log(`🎬 [AUDIO-CONTINUITY] Re-syncing ${track2AudioClips.length} audio clips`)

    // 重新同步每个音频片段
    const syncPromises = track2AudioClips.map(async (clip) => {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      console.log(
        `🎬 [AUDIO-CONTINUITY] Re-syncing clip ${clip.id} to time ${targetAudioTime.toFixed(2)}s`,
      )

      // 确保音频在正确的时间位置
      try {
        audio.currentTime = targetAudioTime

        // 如果音频暂停了，重新启动
        if (audio.paused) {
          await audio.play()
        }

        // 短暂等待确保音频稳定
        await new Promise((resolve) => setTimeout(resolve, 100))
      } catch (error) {
        console.warn(`🎬 [AUDIO-CONTINUITY] Failed to sync clip ${clip.id}:`, error)
      }
    })

    await Promise.all(syncPromises)

    // 更新音频混合器
    this.audioMixer.updateTime(time, true)

    console.log('🎬 [AUDIO-CONTINUITY] Audio continuity restored')
  }

  /**
   * 严格等待音频完全准备完成
   */
  private async waitForAudioFullyReady(time: number): Promise<void> {
    console.log('🎬 [AUDIO-WAIT] Starting strict audio readiness check')

    const activeClips = this.audioMixer.getActiveClips(time)
    const track2AudioClips = activeClips.filter((clip) => {
      return clip.trackId === 'track-2' || clip.id.includes('track-2') || clip.id.endsWith('_audio')
    })

    if (track2AudioClips.length === 0) {
      console.log('🎬 [AUDIO-WAIT] No track-2 audio clips found, proceeding')
      return
    }

    console.log(`🎬 [AUDIO-WAIT] Checking ${track2AudioClips.length} track-2 audio clips`)

    // 为每个音频片段进行严格的准备检查
    const audioReadyPromises = track2AudioClips.map(async (clip, index) => {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      console.log(
        `🎬 [AUDIO-WAIT] Checking clip ${index + 1}/${track2AudioClips.length}: ${clip.id}`,
      )

      return new Promise<void>((resolve) => {
        let checkCount = 0
        const maxChecks = 100 // 最多检查100次，每次100ms，总共10秒

        const checkAudioReady = () => {
          checkCount++

          // 检查音频数据是否充分加载
          const hasEnoughData = audio.readyState >= audio.HAVE_CURRENT_DATA
          const hasCorrectTime = Math.abs(audio.currentTime - targetAudioTime) < 0.1
          const isNotPaused = !audio.paused
          const hasValidDuration = audio.duration > 0 && !isNaN(audio.duration)

          console.log(
            `🎬 [AUDIO-WAIT] Clip ${index + 1} check ${checkCount}: readyState=${audio.readyState}, hasEnoughData=${hasEnoughData}, hasCorrectTime=${hasCorrectTime}, isNotPaused=${isNotPaused}, hasValidDuration=${hasValidDuration}`,
          )

          if (hasEnoughData && hasCorrectTime && isNotPaused && hasValidDuration) {
            console.log(`🎬 [AUDIO-WAIT] ✅ Clip ${index + 1} is fully ready`)
            resolve()
            return
          }

          // 如果音频还没准备好，尝试修复
          if (!hasEnoughData) {
            console.log(`🎬 [AUDIO-WAIT] 🔄 Clip ${index + 1} loading data...`)
            audio.load()
          }

          if (!hasCorrectTime) {
            console.log(`🎬 [AUDIO-WAIT] 🔄 Clip ${index + 1} setting time to ${targetAudioTime}`)
            try {
              audio.currentTime = targetAudioTime
            } catch (error) {
              console.warn(`🎬 [AUDIO-WAIT] ⚠️ Failed to set time for clip ${index + 1}:`, error)
            }
          }

          if (audio.paused) {
            console.log(`🎬 [AUDIO-WAIT] 🔄 Clip ${index + 1} starting playback...`)
            audio.play().catch((error) => {
              console.warn(`🎬 [AUDIO-WAIT] ⚠️ Failed to play clip ${index + 1}:`, error)
            })
          }

          // 如果达到最大检查次数，强制resolve避免无限等待
          if (checkCount >= maxChecks) {
            console.warn(
              `🎬 [AUDIO-WAIT] ⚠️ Clip ${index + 1} max checks reached, proceeding anyway`,
            )
            resolve()
            return
          }

          // 继续检查
          setTimeout(checkAudioReady, 100)
        }

        checkAudioReady()
      })
    })

    // 等待所有音频片段都准备好
    await Promise.all(audioReadyPromises)

    console.log('🎬 [AUDIO-WAIT] ✅ All audio clips are fully ready')

    // 额外等待500ms确保音频播放稳定
    await new Promise((resolve) => setTimeout(resolve, 500))

    console.log('🎬 [AUDIO-WAIT] ✅ Audio readiness check completed')
  }

  /**
   * 重新加载轨道二的所有视频
   */
  public reloadTrack2Videos(): void {
    const track2 = this.videoTracks.get('track-2')
    if (!track2) {
      console.log('🎵 [RELOAD] Track-2 not found')
      return
    }

    const allClips = track2.getClips()
    console.log(`🎵 [RELOAD] Reloading ${allClips.length} clips in track-2`)

    // 第一阶段：重新加载所有视频
    allClips.forEach((clip, index) => {
      const video = clip.videoElement
      const originalSrc = video.src
      const originalPreload = video.preload

      // 暂停并重置视频
      video.pause()
      video.currentTime = 0

      // 清除缓存并重新加载
      video.removeAttribute('src')
      video.load()

      // 延迟重新设置src，确保完全重新加载
      setTimeout(() => {
        video.src = originalSrc
        video.preload = 'auto' // 强制设置为auto，确保积极预加载
        video.load()

        // 设置到正确的开始时间
        setTimeout(() => {
          try {
            video.currentTime = clip.trimStart
            console.log(`🎵 [RELOAD] Clip ${index + 1} reloaded and set to ${clip.trimStart}s`)
          } catch (error) {
            console.warn(`🎵 [RELOAD] Failed to set time for clip ${index + 1}:`, error)
          }
        }, 100)
      }, index * 50) // 每个片段延迟50ms，避免同时加载
    })

    // 第二阶段：预缓存操作
    setTimeout(() => {
      this.performTrack2Precaching()
    }, 1500) // 1.5秒后开始预缓存，给重新加载时间

    // 第三阶段：重新标记轨道二为稳定状态
    setTimeout(() => {
      track2.setStable(true)
      console.log('🎵 [RELOAD] Track-2 marked as stable after reload and precaching')
    }, 3000) // 3秒后标记为稳定，给预缓存更多时间
  }

  /**
   * 执行轨道二的预缓存操作
   */
  private performTrack2Precaching(): void {
    const track2 = this.videoTracks.get('track-2')
    if (!track2) return

    const allClips = track2.getClips()
    console.log(`🎵 [PRECACHE] Starting precaching for ${allClips.length} clips in track-2`)

    // 预缓存前3个片段，确保开始播放时流畅
    const clipsToPreload = allClips.slice(0, 3)

    clipsToPreload.forEach((clip, index) => {
      const video = clip.videoElement
      const isFreezeFrame = (video as any)._isFreezeFrame

      if (isFreezeFrame) return // 跳过定格帧

      // 延迟预缓存，避免同时处理太多视频
      setTimeout(() => {
        // 设置积极的预加载策略
        video.preload = 'auto'

        // 如果视频数据不足，强制加载
        if (video.readyState < video.HAVE_CURRENT_DATA) {
          video.load()
          console.log(`🎵 [PRECACHE] Loading clip ${index + 1}`)

          // 等待数据加载
          const checkLoaded = () => {
            if (video.readyState >= video.HAVE_CURRENT_DATA) {
              console.log(`🎵 [PRECACHE] Clip ${index + 1} loaded successfully`)

              // 预设到正确的时间点
              setTimeout(() => {
                try {
                  video.currentTime = clip.trimStart
                  console.log(`🎵 [PRECACHE] Clip ${index + 1} time set to ${clip.trimStart}s`)
                } catch (error) {
                  console.warn(`🎵 [PRECACHE] Failed to set time for clip ${index + 1}:`, error)
                }
              }, 100)
            } else {
              // 如果还没加载完，继续等待
              setTimeout(checkLoaded, 200)
            }
          }

          setTimeout(checkLoaded, 100)
        } else {
          console.log(`🎵 [PRECACHE] Clip ${index + 1} already loaded`)

          // 确保时间设置正确
          setTimeout(() => {
            try {
              video.currentTime = clip.trimStart
            } catch (error) {
              console.warn(`🎵 [PRECACHE] Failed to set time for clip ${index + 1}:`, error)
            }
          }, 100)
        }
      }, index * 300) // 每个片段延迟300ms，给足够的处理时间
    })

    // 预缓存完成后的回调
    setTimeout(
      () => {
        console.log('🎵 [PRECACHE] Track-2 precaching completed')

        // 触发一次渲染，确保第一帧正确显示
        this.renderFrame()
      },
      clipsToPreload.length * 300 + 1000,
    ) // 等待所有预缓存完成
  }

  /**
   * 开始录制 - 使用预览时的音频准备逻辑
   */
  public async startRecording(): Promise<void> {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      return
    }

    console.log('🎬 [RECORDING] Starting recording with preview-based audio logic')

    // 启用增强音频处理
    try {
      await this.audioMixer.enableEnhancedProcessing()
    } catch (error) {
      console.warn('🎬 [RECORDING] Enhanced processing warning:', error)
    }

    // 启用录制模式，只处理轨道二的音频
    this.audioMixer.setRecordingMode(true)

    // 确保AudioContext处于运行状态
    await this.audioMixer.ensureAudioContextRunning()

    const currentTime = this.getCurrentTime()

    // 使用预览时的完整音频准备流程
    console.log('🎬 [RECORDING] Using preview audio preparation logic')

    // 1. 确保AudioContext在用户交互后能正确启动
    try {
      await this.audioMixer.ensureAudioContextRunning()
    } catch (error) {
      console.warn('🎬 [RECORDING] AudioContext startup warning:', error)
    }

    // 2. 如果音频还没准备好，先准备音频（但不播放）
    await this.ensureCurrentFrameAudioReady(currentTime)

    // 3. 准备视频轨道
    await this.prepareVideoTracks(currentTime)

    // 4. 最终音频验证
    await this.finalAudioSync(currentTime)

    // 5. 现在开始真正的播放（使用预览时的startActualPlayback逻辑）
    await this.startActualPlayback(currentTime)

    // 6. 严格等待音频完全准备完成
    console.log(
      '🎬 [RECORDING] Waiting for audio to be fully ready before creating recording stream',
    )
    await this.waitForAudioFullyReady(currentTime)

    // 等待音频完全稳定
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 创建录制流
    console.log('🎬 [RECORDING] Creating recording stream after audio is ready')
    this.recordingStream = await this.createCompatibleRecordingStream()

    // 录制流创建后验证音频流质量
    await this.verifyAudioStreamQuality()

    // 最终同步等待（确保录制流也完全准备好）
    await new Promise((resolve) => setTimeout(resolve, 800)) // 录制流准备等待

    this.recordingStream.getAudioTracks().forEach((track, index) => {
      // 检查音频轨道是否有实际数据
      const audioContext = new AudioContext()
      const source = audioContext.createMediaStreamSource(new MediaStream([track]))
      const analyser = audioContext.createAnalyser()
      source.connect(analyser)

      const dataArray = new Uint8Array(analyser.frequencyBinCount)

      // 检查几次音频数据
      let checkCount = 0
      const checkAudioData = () => {
        analyser.getByteFrequencyData(dataArray)
        checkCount++
        if (checkCount < 3) {
          setTimeout(checkAudioData, 500)
        } else {
          // 清理
          audioContext.close()
        }
      }

      setTimeout(checkAudioData, 100)
    })
    // 注意：MediaRecorder的创建和启动将在startRecording方法完成后，
    // 在exportVideo方法中进行，确保音频完全稳定后才开始录制
  }

  /**
   * 停止录制并返回录制的视频Blob
   */
  public stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || this.mediaRecorder.state !== 'recording') {
        reject(new Error('No active recording'))
        return
      }

      this.mediaRecorder.onstop = () => {
        // 根据录制时使用的格式创建Blob
        const mimeType = this.mediaRecorder?.mimeType || 'video/mp4'
        const blob = new Blob(this.recordedChunks, { type: mimeType })

        this.recordedChunks = []

        // 禁用录制模式，恢复正常音频处理
        this.audioMixer.setRecordingMode(false)

        // 清理录制流
        if (this.recordingStream) {
          this.recordingStream.getTracks().forEach((track) => track.stop())
          this.recordingStream = undefined
        }

        resolve(blob)
      }

      this.mediaRecorder.stop()
    })
  }

  /**
   * 导出视频（客户端导出）
   */
  public async exportVideo(progressCallback?: (progress: number) => void): Promise<Blob> {
    // 保存当前状态，但不暂停（避免清理预缓存）
    const mainTrack = this.videoTracks.get('track-1')
    const wasPlaying = mainTrack ? mainTrack.getState().isPlaying : false
    const originalTime = this.getCurrentTime()

    const totalDuration = this.getTotalDuration()

    if (totalDuration <= 0) {
      throw new Error('No video content to export')
    }

    // 初始化进度
    if (progressCallback) {
      progressCallback(0)
    }

    console.log('🎬 [EXPORT] Starting video export with enhanced preparation')

    // 第一步：暂停并重置到开始位置
    this.pause()
    await new Promise((resolve) => setTimeout(resolve, 300))

    // 第二步：重新加载轨道二视频并预缓存（跟停止键一样）
    console.log('🎬 [EXPORT] Reloading track-2 videos before recording')
    this.reloadTrack2Videos()

    // 等待重新加载和预缓存完成
    await new Promise((resolve) => setTimeout(resolve, 4000)) // 等待4秒，确保重新加载和预缓存完成

    // 第三步：重置到开始位置，确保从头开始录制
    await this.setCurrentTime(0)
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 第四步：使用预览时的完整音频准备流程
    console.log('🎬 [EXPORT] Preparing audio using preview logic')

    // 1. 确保AudioContext在用户交互后能正确启动
    try {
      await this.audioMixer.ensureAudioContextRunning()
    } catch (error) {
      console.warn('🎬 [EXPORT] AudioContext startup warning:', error)
    }

    // 2. 如果音频还没准备好，先准备音频（但不播放）
    await this.ensureCurrentFrameAudioReady(0)

    // 3. 准备视频轨道
    await this.prepareVideoTracks(0)

    // 4. 最终音频验证
    await this.finalAudioSync(0)

    // 额外等待确保音频完全稳定
    await new Promise((resolve) => setTimeout(resolve, 800))

    // 第五步：使用预览时的播放启动逻辑
    console.log('🎬 [EXPORT] Starting playback using preview logic')

    // 5. 现在开始真正的播放（使用预览时的startActualPlayback逻辑）
    await this.startActualPlayback(0)

    // 等待播放状态稳定
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 强制渲染第一帧，确保导出开始时有正确的画面
    this.renderFrame()

    // 等待视频稳定
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 确保所有视频元素都已经准备好第一帧
    await this.ensureVideoElementsReady()

    // 录制前进行定格帧后片段的特殊预加载
    await this.preloadPostFreezeSegments()

    // 启用录制模式，只处理轨道二的音频（在创建MediaRecorder之前）
    console.log('🎬 [EXPORT] Enabling recording mode')
    this.audioMixer.setRecordingMode(true)

    // 强制确保所有音频数据完全加载
    await this.forceLoadAllAudioData()

    // 第六步：严格等待音频完全准备完成
    console.log('🎬 [EXPORT] Waiting for audio to be fully ready before recording')
    await this.waitForAudioFullyReady(0)

    // 确保时间轴重置后，重新启动播放状态
    console.log('🎬 [EXPORT] Final playback restart after audio ready')
    await this.startActualPlayback(0) // 使用预览时的播放逻辑
    await new Promise((resolve) => setTimeout(resolve, 500)) // 等待播放状态稳定

    // 再次确认时间轴从0开始
    this.setCurrentTime(0)

    // 创建MediaRecorder（在音频完全稳定后）

    if (!this.recordingStream) {
      // 尝试重新创建录制流
      try {
        this.recordingStream = await this.createCompatibleRecordingStream()

        if (!this.recordingStream) {
          throw new Error('Failed to create recording stream on retry')
        }
      } catch (error) {
        throw new Error(`Recording stream creation failed: ${error}`)
      }
    }

    this.mediaRecorder = this.createCompatibleMediaRecorder(this.recordingStream)

    this.recordedChunks = []

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data)
        // 检查数据块的类型和内容
      } else {
      }
    }

    this.mediaRecorder.onerror = (event) => {}

    // 开始录制（使用更大的时间片减少音频断续）
    this.mediaRecorder.start(1000) // 增加到1000ms时间片，减少音频断续

    // 启动音频流监控（降低频率）
    this.startAudioStreamMonitoring()

    // 开始录制流程（音频已经在startRecording中完全准备好）
    console.log('🎬 [EXPORT] Starting recording process with fully prepared audio')
    await this.startRecording()

    // 最后一次确认音频完全准备好再开始MediaRecorder录制
    console.log('🎬 [EXPORT] Final audio readiness check before MediaRecorder start')
    await this.waitForAudioFullyReady(0)

    // 等待录制器准备就绪，并确保第一帧已经渲染
    await new Promise((resolve) => setTimeout(resolve, 1000)) // 增加到1秒等待时间

    // 再次强制渲染当前帧，确保录制开始时有正确的画面
    this.renderFrame()

    // 短暂等待确保渲染完成
    await new Promise((resolve) => setTimeout(resolve, 200))

    // 报告录制开始进度
    if (progressCallback) {
      progressCallback(5) // 5% - 录制已开始
    }

    // 等待播放完成
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      const maxWaitTime = (totalDuration + 30) * 1000 // 增加等待时间，最多等待视频时长+30秒

      // 音频准备等待变量
      let recordingStartTime = Date.now() // 实际开始录制的时间
      let hasStartedValidRecording = false
      const audioPreparationStartTime = Date.now() // 音频准备开始时间
      const maxAudioWaitTime = 30 * 1000 // 最多等待30秒音频准备

      const checkCompletion = async () => {
        const currentTime = this.getCurrentTime()
        const elapsedTime = Date.now() - startTime
        const audioWaitTime = Date.now() - audioPreparationStartTime

        // 如果还没开始有效录制，持续检查音频是否准备好
        if (!hasStartedValidRecording) {
          console.log(
            `🎬 [AUDIO-PREP] Checking audio readiness... (waited ${(audioWaitTime / 1000).toFixed(1)}s)`,
          )

          // 检查音频是否完全准备好
          const isAudioReady = await this.checkAudioFullyReady(currentTime)

          if (isAudioReady) {
            console.log(
              `🎬 [AUDIO-PREP] ✅ Audio is fully ready after ${(audioWaitTime / 1000).toFixed(1)}s, starting recording`,
            )
            hasStartedValidRecording = true
            recordingStartTime = Date.now()

            // 确保MediaRecorder处于录制状态
            if (this.mediaRecorder && this.mediaRecorder.state === 'paused') {
              this.mediaRecorder.resume()
            }
          } else {
            // 音频还没准备好，继续等待
            if (audioWaitTime > maxAudioWaitTime) {
              console.warn(
                `🎬 [AUDIO-PREP] ⚠️ Audio preparation timeout after ${maxAudioWaitTime / 1000}s, starting recording anyway`,
              )
              hasStartedValidRecording = true
              recordingStartTime = Date.now()

              // 强制开始录制
              if (this.mediaRecorder && this.mediaRecorder.state === 'paused') {
                this.mediaRecorder.resume()
              }
            } else {
              console.log(`🎬 [AUDIO-PREP] 🔄 Audio not ready yet, continuing to wait...`)

              // 暂停MediaRecorder直到音频准备好
              if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
                console.log(`🎬 [AUDIO-PREP] Pausing MediaRecorder until audio is ready`)
                this.mediaRecorder.pause()
              }
            }
          }
        }

        // 计算有效录制时长（从有效录制开始算起）
        const validRecordingDuration = hasStartedValidRecording
          ? (Date.now() - recordingStartTime) / 1000
          : 0

        // 计算进度 - 使用有效录制时间
        const playbackProgress = Math.min(validRecordingDuration / totalDuration, 1)
        const totalProgress = 5 + playbackProgress * 85 // 5% 到 90%

        if (progressCallback) {
          progressCallback(Math.round(totalProgress))
        }

        // 持续更新音频混合器，确保音频跟随播放进度
        if (this.audioMixer) {
          this.audioMixer.updateTime(currentTime, true)
        }

        // 如果使用了实时音频混合器，也要更新它
        if ((this as any).audioMixerUpdate) {
          ;(this as any).audioMixerUpdate()
        }

        // 使用有效录制时间来判断停止条件
        if (
          (hasStartedValidRecording && validRecordingDuration >= totalDuration) ||
          elapsedTime > maxWaitTime
        ) {
          // 播放完成或超时，停止录制
          this.pause()

          // 报告录制完成，开始文件处理
          if (progressCallback) {
            progressCallback(90) // 90% - 录制完成，开始文件处理
          }

          // 记录录制统计信息
          const audioWaitDuration = (Date.now() - audioPreparationStartTime) / 1000
          console.log(`🎬 [RECORDING-STATS] Recording completed:`)
          console.log(
            `🎬 [RECORDING-STATS] - Audio preparation time: ${audioWaitDuration.toFixed(2)}s`,
          )
          console.log(
            `🎬 [RECORDING-STATS] - Valid recording duration: ${validRecordingDuration.toFixed(2)}s`,
          )
          console.log(`🎬 [RECORDING-STATS] - Target duration: ${totalDuration.toFixed(2)}s`)
          console.log(
            `🎬 [RECORDING-STATS] - Recording efficiency: ${((validRecordingDuration / totalDuration) * 100).toFixed(1)}%`,
          )

          this.stopRecording()
            .then(async (blob) => {
              // 恢复原始状态
              await this.setCurrentTime(originalTime)
              if (wasPlaying) {
                await this.play() // 等待音频准备完成
              } else {
                this.pause()
              }

              if (blob.size === 0) {
                reject(new Error('Exported video is empty (0 bytes)'))
              } else {
                // 最终完成，包含音频准备统计
                console.log(
                  `🎬 [EXPORT-COMPLETE] Video exported successfully after ${audioWaitDuration.toFixed(2)}s audio preparation`,
                )
                if (progressCallback) {
                  progressCallback(100) // 100% - 导出完成
                }
                resolve(blob)
              }
            })
            .catch(reject)
        } else {
          // 继续检查，录制时使用更低的检查频率减少干扰
          const checkInterval = this.isRecording() ? 200 : 100
          setTimeout(() => {
            checkCompletion().catch((error) => {
              console.error('🎬 [ERROR] Error in checkCompletion:', error)
              reject(error)
            })
          }, checkInterval)
        }
      }

      // 启动检查循环
      checkCompletion().catch((error) => {
        console.error('🎬 [ERROR] Error in initial checkCompletion:', error)
        reject(error)
      })
    })
  }

  /**
   * 渲染当前帧（优化版本，减少闪烁）
   */
  private renderFrame(): void {
    try {
      const currentTime = this.getCurrentTime()
      const isRecording = this.isRecording()

      // 收集所有轨道的活跃片段，按轨道顺序分层
      const allActiveClips: { clip: any; trackOrder: number; trackId: string }[] = []

      // 按轨道顺序收集片段（track-1在底层，track-2在上层）
      const trackOrder = ['track-1', 'track-2']
      trackOrder.forEach((trackId, order) => {
        const track = this.videoTracks.get(trackId)
        if (track) {
          const activeClips = track.getActiveClips(currentTime)
          activeClips.forEach((clip) => {
            // 检查是否为定格帧
            const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame

            if (isFreezeFrame) {
              // 定格帧总是准备好的
              allActiveClips.push({ clip, trackOrder: order, trackId })
            } else {
              // 检查视频准备状态，录制时对所有视频使用更宽容的要求
              const isUrlVideo = clip.videoElement.src.startsWith('http')
              const minReadyState = isRecording
                ? clip.videoElement.HAVE_METADATA // 录制时统一使用较低要求
                : isUrlVideo
                  ? clip.videoElement.HAVE_METADATA
                  : clip.videoElement.HAVE_CURRENT_DATA

              const readyStateOK = clip.videoElement.readyState >= minReadyState
              const widthOK = clip.videoElement.videoWidth > 0
              const heightOK = clip.videoElement.videoHeight > 0
              const noError = !clip.videoElement.error

              const isReady = readyStateOK && widthOK && heightOK && noError

              if (isReady) {
                allActiveClips.push({ clip, trackOrder: order, trackId })
              } else {
                // 使用预览的简单逻辑：对于视频切换边界，使用更宽容的准备状态检查
                const isClippedVideo = clip.trimStart > 0
                const isAtStart = Math.abs(currentTime - clip.startTime) < 0.1 // 开始100ms内

                if (
                  isClippedVideo &&
                  clip.videoElement.readyState < clip.videoElement.HAVE_METADATA
                ) {
                  // 切分片段可能需要更多时间准备，暂时跳过但不警告
                  return
                }

                // 如果是在片段开始附近且视频有基本数据，也允许渲染
                if (isAtStart && clip.videoElement.readyState >= clip.videoElement.HAVE_METADATA) {
                  allActiveClips.push({ clip, trackOrder: order, trackId })
                }
              }
            }
          })
        }
      })

      // 调试：记录最终收集到的片段数量
      if (isRecording) {
      }

      // 先清空画布
      // 只有有内容时才清空画布
      if (allActiveClips.length > 0) {
        this.renderer.clear()
      }

      // 如果没有活跃片段，检查是否应该清空画布
      if (allActiveClips.length === 0) {
        if (isRecording) {
        }
        // 检查当前时间与最近片段的距离
        const shouldClearCanvas = this.shouldClearCanvasAtCurrentTime(currentTime)

        if (shouldClearCanvas) {
          // 清空画布，避免显示缓存的上一帧内容
          this.renderer.clear()
          if (isRecording) {
          }
        } else {
          // 如果不需要清空，保留上一帧（用于短暂的片段间隙）
          if (isRecording) {
          }
        }

        this._lastFrameHadContent = false
        return
      }
      this._lastFrameHadContent = true

      // 调试：记录成功渲染
      if (isRecording) {
      }

      // 按轨道顺序渲染（底层到顶层）
      allActiveClips.forEach((item) => {
        const { clip } = item

        try {
          this.renderer.renderVideoFrame(clip.videoElement, 1.0, false, clip.transform, isRecording)
        } catch (error) {
          //
        }
      })

      // 渲染文字片段（在视频之上）
      const activeTextClips = this.getActiveTextClips()
      this.renderer.renderTextClips(activeTextClips)

      // 调试：记录渲染完成
      if (isRecording) {
      }
    } catch (error) {
      // 发生错误时清空画布，避免显示错误内容
      this.renderer.clear()
    }
  }

  /**
   * 检查当前时间是否应该清空画布
   */
  private shouldClearCanvasAtCurrentTime(currentTime: number): boolean {
    // 如果正在录制，使用更严格的条件，避免录制中出现黑屏
    const gapThreshold = this.isRecording() ? 2.0 : 0.5 // 录制时需要2秒间隙才清空，预览时0.5秒

    // 收集所有轨道的所有片段
    const allClips: { startTime: number; endTime: number }[] = []

    this.videoTracks.forEach((track) => {
      const clips = track.getAllClips()
      clips.forEach((clip) => {
        allClips.push({
          startTime: clip.startTime,
          endTime: clip.startTime + clip.duration,
        })
      })
    })

    if (allClips.length === 0) {
      return true // 没有片段时清空画布
    }

    // 按时间排序
    allClips.sort((a, b) => a.startTime - b.startTime)

    // 检查当前时间是否在任何片段的间隙中，且间隙大于阈值
    for (let i = 0; i < allClips.length - 1; i++) {
      const currentClipEnd = allClips[i].endTime
      const nextClipStart = allClips[i + 1].startTime
      const gap = nextClipStart - currentClipEnd

      // 如果当前时间在间隙中，且间隙大于阈值
      if (currentTime >= currentClipEnd && currentTime < nextClipStart && gap > gapThreshold) {
        return true
      }
    }

    // 检查是否在所有片段之前或之后
    const firstClipStart = allClips[0].startTime
    const lastClipEnd = allClips[allClips.length - 1].endTime

    if (currentTime < firstClipStart - gapThreshold || currentTime > lastClipEnd + gapThreshold) {
      return true
    }

    return false
  }

  /**
   * 更新音频时间
   */
  private updateAudioTime(time: number): void {
    // 检查是否有轨道正在播放
    let isPlaying = false
    for (const [, track] of this.videoTracks) {
      if (track.getState().isPlaying) {
        isPlaying = true
        break
      }
    }
    this.audioMixer.updateTime(time, isPlaying)
  }

  /**
   * 确保当前帧音频完全准备好（只处理轨道2音频，不播放）
   */
  private async ensureCurrentFrameAudioReady(time: number): Promise<void> {
    // 1. 先暂停所有音频，确保干净的状态
    this.audioMixer.pauseAll()
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 2. 获取当前时间点的活跃音频片段
    const allActiveClips = this.audioMixer.getActiveClips(time)

    // 3. 只处理轨道2的音频片段（轨道1默认没有音频）
    const track2AudioClips = allActiveClips.filter((clip) => {
      // 检查是否是轨道2的音频
      return clip.trackId === 'track-2' || clip.id.includes('track-2') || clip.id.endsWith('_audio')
    })

    if (track2AudioClips.length === 0) {
      return
    }

    // 4. 强制加载和准备轨道2的音频片段（但不播放）
    const preparationPromises = track2AudioClips.map(async (clip, index) => {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      // 强制重新加载如果数据不足
      if (audio.readyState < audio.HAVE_CURRENT_DATA) {
        audio.load()

        // 等待数据加载完成
        await new Promise<void>((resolve) => {
          const checkReady = () => {
            if (audio.readyState >= audio.HAVE_CURRENT_DATA) {
              resolve()
            } else {
              setTimeout(checkReady, 50)
            }
          }

          // 设置超时避免无限等待
          setTimeout(() => {
            resolve()
          }, 2000)

          checkReady()
        })
      }

      // 精确设置音频时间（录制期间跳过）
      try {
        if (!this.isRecording() && Math.abs(audio.currentTime - targetAudioTime) > 0.05) {
          audio.currentTime = targetAudioTime

          // 等待时间设置完成
          await new Promise<void>((resolve) => {
            const onSeeked = () => {
              audio.removeEventListener('seeked', onSeeked)
              resolve()
            }

            audio.addEventListener('seeked', onSeeked, { once: true })

            // 设置超时
            setTimeout(() => {
              audio.removeEventListener('seeked', onSeeked)
              resolve()
            }, 1000)
          })
        }
      } catch (error) {}

      // 确保音频属性正确，但保持暂停状态
      audio.muted = false
      audio.volume = clip.muted ? 0 : clip.volume
      audio.playbackRate = 1.0

      // 重要：确保音频保持暂停状态，不自动播放
      if (!audio.paused) {
        audio.pause()
      }
    })

    // 等待所有轨道2音频片段准备完成
    await Promise.all(preparationPromises)
  }

  /**
   * 最终音频同步验证（只验证轨道2音频，不播放）
   */
  private async finalAudioSync(time: number): Promise<void> {
    // 验证轨道2音频是否真正准备好
    const allActiveClips = this.audioMixer.getActiveClips(time)

    // 只验证轨道2的音频片段
    const track2AudioClips = allActiveClips.filter((clip) => {
      return clip.trackId === 'track-2' || clip.id.includes('track-2') || clip.id.endsWith('_audio')
    })

    for (const clip of track2AudioClips) {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      // 最终检查音频时间是否正确（录制期间跳过）
      if (!this.isRecording() && Math.abs(audio.currentTime - targetAudioTime) > 0.1) {
        try {
          audio.currentTime = targetAudioTime
        } catch (error) {}
      }

      // 重要：确保音频保持暂停状态
      if (!audio.paused) {
        audio.pause()
      }
    }
  }

  /**
   * 为播放准备音频（确保音频真正准备好）- 增强同步机制
   */
  private async prepareAudioForPlayback(time: number): Promise<void> {
    // 1. 先暂停所有音频，确保干净的状态
    this.audioMixer.pauseAll()

    // 2. 等待短暂时间让音频完全停止
    await new Promise((resolve) => setTimeout(resolve, 50))

    // 3. 预加载当前时间点的音频片段
    await this.preloadAudioClips(time)

    // 4. 等待音频准备完成

    await this.audioMixer.prepareForPlayback(time)

    // 5. 验证音频是否真正准备好
    await this.verifyAudioReadiness(time)
  }

  /**
   * 预加载当前时间点的音频片段
   */
  private async preloadAudioClips(time: number): Promise<void> {
    const activeClips = this.audioMixer.getActiveClips(time)

    const preloadPromises = activeClips.map(async (clip) => {
      return new Promise<void>((resolve) => {
        const audio = clip.audioElement

        // 如果音频已经准备好，直接resolve
        if (audio.readyState >= audio.HAVE_CURRENT_DATA) {
          resolve()
          return
        }

        // 设置超时，避免无限等待
        const timeout = setTimeout(() => {
          resolve()
        }, 2000)

        // 监听数据加载完成
        const onCanPlay = () => {
          clearTimeout(timeout)
          audio.removeEventListener('canplay', onCanPlay)
          audio.removeEventListener('loadeddata', onLoadedData)

          resolve()
        }

        const onLoadedData = () => {
          if (audio.readyState >= audio.HAVE_CURRENT_DATA) {
            onCanPlay()
          }
        }

        audio.addEventListener('canplay', onCanPlay, { once: true })
        audio.addEventListener('loadeddata', onLoadedData, { once: true })

        // 强制加载音频数据
        if (audio.networkState === audio.NETWORK_IDLE) {
          audio.load()
        }
      })
    })

    // 等待所有音频片段预加载完成
    await Promise.all(preloadPromises)
  }

  /**
   * 验证音频是否真正准备好播放
   */
  private async verifyAudioReadiness(time: number): Promise<void> {
    const activeClips = this.audioMixer.getActiveClips(time)

    for (const clip of activeClips) {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      // 检查音频时间是否正确设置（录制期间跳过）
      if (!this.isRecording() && Math.abs(audio.currentTime - targetAudioTime) > 0.1) {
        try {
          audio.currentTime = targetAudioTime
          // 等待时间设置完成
          await new Promise((resolve) => {
            const onSeeked = () => {
              audio.removeEventListener('seeked', onSeeked)
              resolve(undefined)
            }
            audio.addEventListener('seeked', onSeeked, { once: true })

            // 设置超时避免无限等待
            setTimeout(() => {
              audio.removeEventListener('seeked', onSeeked)
              resolve(undefined)
            }, 500)
          })
        } catch (error) {}
      }
    }
  }

  /**
   * 深度准备音频用于录制（确保音频完全就绪）
   */
  private async deepPrepareAudioForRecording(time: number): Promise<void> {
    // 1. 获取当前时间点的活跃音频片段
    const activeClips = this.audioMixer.getActiveClips(time)

    if (activeClips.length === 0) {
      return
    }

    // 2. 为每个音频片段进行深度准备
    const preparationPromises = activeClips.map(async (clip) => {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      // 强制重新加载音频数据
      if (audio.readyState < audio.HAVE_CURRENT_DATA) {
        audio.load()

        // 等待数据加载完成
        await new Promise((resolve) => {
          const checkReady = () => {
            if (audio.readyState >= audio.HAVE_CURRENT_DATA) {
              resolve(undefined)
            } else {
              setTimeout(checkReady, 100)
            }
          }

          // 设置超时
          setTimeout(() => {
            resolve(undefined)
          }, 3000)

          checkReady()
        })
      }

      // 精确设置音频时间（录制期间跳过）
      if (!this.isRecording()) {
        try {
          audio.currentTime = targetAudioTime
          // 等待时间设置完成
          await new Promise((resolve) => {
            const onSeeked = () => {
              audio.removeEventListener('seeked', onSeeked)
              resolve(undefined)
            }

            audio.addEventListener('seeked', onSeeked, { once: true })

            // 设置超时
            setTimeout(() => {
              audio.removeEventListener('seeked', onSeeked)
              resolve(undefined)
            }, 1000)
          })
        } catch (error) {}
      } else {
        console.log(`🎬 [RECORDING-SKIP] Skipping precise audio time setting during recording`)
      }

      // 确保音频元素处于正确状态
      audio.muted = false
      audio.volume = clip.volume
    })

    // 等待所有音频片段准备完成
    await Promise.all(preparationPromises)

    // 3. 调用AudioMixer的准备方法
    await this.audioMixer.prepareForPlayback(time)
  }

  /**
   * 强制启动音频播放，确保音频流活跃稳定
   */
  private async forceStartAudioPlayback(time: number): Promise<void> {
    const activeClips = this.audioMixer.getActiveClips(time)

    if (activeClips.length === 0) {
      return
    }

    // 强制启动每个音频片段的播放
    const playPromises = activeClips.map(async (clip) => {
      const audio = clip.audioElement
      const relativeTime = time - clip.startTime
      const targetAudioTime = clip.trimStart + relativeTime

      try {
        // 录制期间不强制设置音频时间，避免回弹
        if (!this.isRecording()) {
          // 确保音频时间正确
          if (Math.abs(audio.currentTime - targetAudioTime) > 0.05) {
            audio.currentTime = targetAudioTime
          }
        } else {
          console.log(
            `🎬 [RECORDING-SKIP] Skipping audio time setting during recording to avoid time jumps`,
          )
        }

        // 确保音频设置正确
        audio.muted = false
        audio.volume = clip.volume
        audio.playbackRate = 1.0

        // 强制播放
        if (audio.paused) {
          await audio.play()
        }

        // 等待音频稳定播放
        await new Promise((resolve) => {
          const checkPlaying = () => {
            if (!audio.paused && audio.currentTime > 0) {
              resolve(undefined)
            } else {
              setTimeout(checkPlaying, 50)
            }
          }

          // 设置超时
          setTimeout(() => {
            resolve(undefined)
          }, 2000)

          checkPlaying()
        })
      } catch (error) {}
    })

    // 等待所有音频片段启动完成
    await Promise.all(playPromises)
  }

  /**
   * 启动音频流监控
   */
  private startAudioStreamMonitoring(): void {
    if (!this.recordingStream) {
      return
    }

    // 每2秒检查一次音频流状态（降低频率，减少干扰）
    const monitorInterval = setInterval(() => {
      if (
        !this.recordingStream ||
        !this.mediaRecorder ||
        this.mediaRecorder.state !== 'recording'
      ) {
        clearInterval(monitorInterval)
        return
      }

      const audioTracks = this.recordingStream.getAudioTracks()
      const currentTime = this.getCurrentTime()

      // 检查每个音频轨道
      audioTracks.forEach((track, index) => {
        if (!track.enabled || track.muted || track.readyState !== 'live') {
          // 尝试修复音频轨道
          if (!track.enabled) {
            track.enabled = true
          }
        }
      })

      // 检查AudioMixer状态
      if (this.audioMixer) {
        const activeClips = this.audioMixer.getActiveClips(currentTime)

        activeClips.forEach(async (clip, index) => {
          try {
            if (!clip) {
              return
            }

            const audio = clip.audioElement
            if (!audio) {
              return
            }

            const relativeTime = currentTime - clip.startTime
            const targetAudioTime = clip.trimStart + relativeTime

            // 检查音频数据是否充分加载
            if (audio.readyState < 2) {
              try {
                audio.load() // 强制重新加载音频
              } catch (loadError) {}
            }

            // 检查音频是否暂停
            if (audio.paused) {
              audio.play()
            }
          } catch (error) {
            //
          }
        })
      }
    }, 2000) // 每2秒检查一次，减少对音频播放的干扰
  }

  /**
   * 强制加载所有音频数据，确保readyState >= 2（不启动播放）
   */
  private async forceLoadAllAudioData(): Promise<void> {
    // 确保时间轴停止，防止意外播放
    this.pause()
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 获取所有音频片段（不依赖当前时间）
    const allAudioClips = this.audioMixer.getAllClips() // 获取所有音频片段，不管时间

    const loadPromises = allAudioClips.map(async (clip: any, index: number) => {
      const audio = clip.audioElement
      const clipId = clip.id.slice(-6)
      // 如果已经有足够的数据，跳过
      if (audio.readyState >= 2) {
        return
      }

      return new Promise<void>((resolve) => {
        const timeout = setTimeout(() => {
          resolve()
        }, 3000) // 减少到3秒超时

        const checkReady = () => {
          if (audio.readyState >= 2) {
            clearTimeout(timeout)
            resolve()
          } else {
            setTimeout(checkReady, 100)
          }
        }

        // 强制重新加载，但确保不播放
        try {
          // 确保音频暂停状态
          audio.pause()
          audio.currentTime = 0
          audio.load()
          checkReady()
        } catch (error) {
          clearTimeout(timeout)
          resolve()
        }
      })
    })

    await Promise.all(loadPromises)

    // 确保时间轴重置到0
    this.setCurrentTime(0)
  }

  /**
   * 验证音频流质量
   */
  private async verifyAudioStreamQuality(): Promise<void> {
    if (!this.recordingStream) {
      return
    }

    const audioTracks = this.recordingStream.getAudioTracks()

    if (audioTracks.length === 0) {
      return
    }

    // 检查每个音频轨道
    audioTracks.forEach((track, index) => {
      // 确保音频轨道启用
      if (!track.enabled) {
        track.enabled = true
      }
    })

    // 使用AudioContext分析音频流质量
    try {
      const audioContext = new AudioContext()
      const source = audioContext.createMediaStreamSource(this.recordingStream)
      const analyser = audioContext.createAnalyser()
      analyser.fftSize = 256
      source.connect(analyser)

      const dataArray = new Uint8Array(analyser.frequencyBinCount)

      // 检查音频数据几次
      for (let i = 0; i < 5; i++) {
        await new Promise((resolve) => setTimeout(resolve, 200))

        analyser.getByteFrequencyData(dataArray)
        const hasAudio = dataArray.some((value) => value > 0)
        const avgLevel = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length

        if (hasAudio && avgLevel > 1) {
          break
        }
      }

      // 清理
      audioContext.close()
    } catch (error) {}
  }

  /**
   * 确保所有视频元素都已经准备好第一帧
   */
  private async ensureVideoElementsReady(): Promise<void> {
    const currentTime = this.getCurrentTime()
    const videoPromises: Promise<void>[] = []

    // 收集当前时间点的所有活跃视频片段
    this.videoTracks.forEach((track) => {
      const activeClips = track.getActiveClips(currentTime)
      activeClips.forEach((clip) => {
        const videoPromise = new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error(`Video element preparation timeout for clip ${clip.id}`))
          }, 2000) // 2秒超时

          const checkReady = () => {
            // 检查视频是否已经有第一帧
            if (
              clip.videoElement.readyState >= 2 && // HAVE_CURRENT_DATA
              clip.videoElement.videoWidth > 0 &&
              clip.videoElement.videoHeight > 0
            ) {
              clearTimeout(timeout)
              resolve()
            } else {
              // 如果还没准备好，继续等待
              setTimeout(checkReady, 50)
            }
          }

          // 确保视频时间设置正确
          const isPreciseSegment = (clip.videoElement as any)._isPreciseSegment

          try {
            // 录制期间不强制设置视频时间，避免回弹
            if (!this.isRecording()) {
              if (isPreciseSegment) {
                // 精确片段使用其内部的时间范围
                const segmentStart = (clip.videoElement as any)._segmentStart
                const segmentEnd = (clip.videoElement as any)._segmentEnd
                const relativeTime = currentTime - clip.startTime
                const videoTime = segmentStart + relativeTime
                // 确保时间在片段范围内
                const clampedTime = Math.max(segmentStart, Math.min(videoTime, segmentEnd))
                if (Math.abs(clip.videoElement.currentTime - clampedTime) > 0.1) {
                  clip.videoElement.currentTime = clampedTime
                }
              } else {
                // 传统片段使用原有逻辑
                const relativeTime = currentTime - clip.startTime
                const videoTime = clip.trimStart + relativeTime

                if (Math.abs(clip.videoElement.currentTime - videoTime) > 0.1) {
                  clip.videoElement.currentTime = videoTime
                }
              }
            } else {
              // 录制期间只检查视频是否准备好，不设置时间
              console.log(
                `🎬 [RECORDING-SKIP] Skipping time setting for clip during recording to avoid time jumps`,
              )
            }

            checkReady()
          } catch (error) {
            clearTimeout(timeout)
            reject(error)
          }
        })

        videoPromises.push(videoPromise)
      })
    })

    // 等待所有视频元素准备完成
    try {
      await Promise.all(videoPromises)
    } catch (error) {
      // 即使有些视频失败，也继续录制
    }
  }

  /**
   * 开始渲染循环（限制帧率）
   */
  private startRenderLoop(): void {
    let lastRenderTime = 0
    const targetFPS = 30 // 限制到30FPS
    const frameInterval = 1000 / targetFPS

    const render = (currentTime: number) => {
      if (currentTime - lastRenderTime >= frameInterval) {
        this.renderFrame()

        // 如果正在录制，在渲染完成后立即请求录制帧
        if (this.isRecording()) {
          this.requestRecordingFrame()
          // 录制时不做任何额外的音频处理，完全依赖预览时的音频更新逻辑
        }

        lastRenderTime = currentTime
      }
      this.animationFrameId = requestAnimationFrame(render)
    }
    this.animationFrameId = requestAnimationFrame(render)
  }

  /**
   * 检查是否正在录制
   */
  private isRecording(): boolean {
    return !!(this.mediaRecorder && this.mediaRecorder.state === 'recording')
  }

  /**
   * 请求录制当前帧（在预览渲染完成后调用）
   */
  private requestRecordingFrame(): void {
    // 手动请求canvas流捕获当前帧
    // 这确保录制帧的捕获与预览渲染完全同步
    if (this.recordingStream) {
      const videoTracks = this.recordingStream.getVideoTracks()
      if (videoTracks.length > 0) {
        const videoTrack = videoTracks[0] as any
        // 手动请求帧（如果支持的话）
        if (videoTrack.requestFrame) {
          videoTrack.requestFrame()
        }
      }
    }
  }

  /**
   * 检查片段是否在定格帧之后（用于录制时的特殊处理）
   */
  private isClipAfterFreezeFrame(clip: any, currentTime: number): boolean {
    // 获取当前轨道的所有片段
    let currentTrack = null
    for (const [trackId, track] of this.videoTracks) {
      const clips = track.getAllClips()
      if (clips.some((c: any) => c.id === clip.id)) {
        currentTrack = track
        break
      }
    }

    if (!currentTrack) return false

    const allClips = currentTrack.getAllClips()

    // 查找当前时间点之前是否有定格帧
    const previousClips = allClips.filter((c: any) => {
      const clipEndTime = c.startTime + c.duration
      return clipEndTime <= currentTime + 0.1 // 允许小的时间误差
    })

    // 检查前面的片段中是否有定格帧
    return previousClips.some((c: any) => (c.videoElement as any)._isFreezeFrame)
  }

  /**
   * 预加载定格帧后的视频片段（录制前的特殊处理）
   */
  private async preloadPostFreezeSegments(): Promise<void> {
    const allPostFreezeSegments: any[] = []

    // 收集所有定格帧后的切分片段
    for (const [trackId, track] of this.videoTracks) {
      const allClips = track.getAllClips()

      for (let i = 0; i < allClips.length; i++) {
        const clip = allClips[i]
        const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame

        // 如果当前片段是定格帧，检查下一个片段
        if (isFreezeFrame && i + 1 < allClips.length) {
          const nextClip = allClips[i + 1]
          const isPreciseSegment = (nextClip.videoElement as any)._isPreciseSegment
          const isCloned = (nextClip.videoElement as any)._isCloned

          // 支持新的精确片段和旧的克隆片段
          if (isPreciseSegment || isCloned) {
            allPostFreezeSegments.push(nextClip)
          }
        }
      }
    }

    // 并行预加载所有定格帧后的片段
    const preloadPromises = allPostFreezeSegments.map(async (clip, index) => {
      const video = clip.videoElement
      const isPreciseSegment = (video as any)._isPreciseSegment
      return new Promise<void>((resolve) => {
        // 精确片段的特殊处理
        if (isPreciseSegment) {
          // 精确片段需要确保时间设置正确
          const segmentStart = (video as any)._segmentStart
          // 确保视频时间设置到片段开始时间
          if (segmentStart !== undefined) {
            video.currentTime = segmentStart
          }
        }

        // 如果已经有足够数据，直接返回
        if (video.readyState >= video.HAVE_ENOUGH_DATA) {
          resolve()
          return
        }

        // 设置超时，避免无限等待
        const timeout = setTimeout(() => {
          resolve()
        }, 5000) // 增加到5秒超时，精确片段可能需要更多时间

        // 监听准备完成
        const onReady = () => {
          clearTimeout(timeout)
          video.removeEventListener('canplaythrough', onReady)
          video.removeEventListener('loadeddata', onReady)
          video.removeEventListener('loadedmetadata', onReady)

          resolve()
        }

        // 监听多个事件
        video.addEventListener('canplaythrough', onReady, { once: true })
        video.addEventListener('loadeddata', onReady, { once: true })

        // 强制加载
        video.preload = 'auto'
        video.load()

        // 设置到正确的开始时间
        try {
          video.currentTime = clip.trimStart
        } catch (error) {
          // 忽略时间设置错误
        }
      })
    })

    // 等待所有预加载完成
    await Promise.allSettled(preloadPromises)
  }

  /**
   * 启动全局预加载策略
   */
  private startGlobalPreloading(): void {
    // 每2秒检查一次预加载状态
    setInterval(() => {
      this.performGlobalPreloading()
    }, 2000)
  }

  /**
   * 执行全局预加载
   */
  private performGlobalPreloading(): void {
    const currentTime = this.getCurrentTime()
    const preloadWindow = 5.0 // 预加载未来5秒内的视频

    this.videoTracks.forEach((track) => {
      const allClips = track.getAllClips()

      // 找到需要预加载的片段
      const clipsToPreload = allClips.filter((clip) => {
        const timeTillStart = clip.startTime - currentTime
        return timeTillStart > 0 && timeTillStart <= preloadWindow
      })

      clipsToPreload.forEach((clip) => {
        this.preloadVideoClip(clip)
      })
    })
  }

  /**
   * 预加载单个视频片段
   */
  private preloadVideoClip(clip: VideoClip): void {
    const video = clip.videoElement
    const isFreezeFrame = (video as any)._isFreezeFrame
    const isCloned = (video as any)._isCloned

    if (isFreezeFrame) {
      return // 定格帧不需要预加载
    }

    // 检查是否需要预加载
    if (video.readyState >= video.HAVE_ENOUGH_DATA) {
      return // 已经加载足够数据
    }

    // 设置预加载策略
    video.preload = 'auto'

    // 对于克隆的视频（切分后的片段），使用更积极的预加载
    if (isCloned) {
      // 强制加载
      video.load()

      // 添加加载完成监听
      const onCanPlay = () => {
        video.removeEventListener('canplay', onCanPlay)
        // 预设到正确的时间点
        try {
          video.currentTime = clip.trimStart
        } catch (error) {
          // 忽略时间设置错误
        }
      }
      video.addEventListener('canplay', onCanPlay, { once: true })

      // 如果200ms后还没有足够数据，再次尝试加载
      setTimeout(() => {
        if (video.readyState < video.HAVE_CURRENT_DATA) {
          video.load()
        }
      }, 200)
    } else {
      // 普通片段的预加载
      if (video.readyState < video.HAVE_METADATA) {
        video.load()
      }
    }
  }

  /**
   * 检测视频元素是否包含音频轨道
   */
  private hasAudioTrack(videoElement: HTMLVideoElement): boolean {
    // 优先使用现代的 audioTracks API
    if ((videoElement as any).audioTracks && (videoElement as any).audioTracks.length > 0) {
      return true
    }

    // 回退到浏览器特定的属性
    if ((videoElement as any).mozHasAudio === true) {
      return true
    }

    if (
      (videoElement as any).webkitAudioDecodedByteCount &&
      (videoElement as any).webkitAudioDecodedByteCount > 0
    ) {
      return true
    }

    // 如果都不可用，假设有音频（保守做法）
    // 在实际使用中，如果没有音频，Audio元素会静默处理
    return true
  }

  /**
   * 获取回退音频轨道（从视频元素直接获取）
   */
  private getFallbackAudioTracks(): MediaStreamTrack[] {
    const audioTracks: MediaStreamTrack[] = []

    try {
      // 遍历所有视频轨道，获取当前活跃的视频元素
      this.videoTracks.forEach((track) => {
        const activeClips = track.getActiveClips(this.getCurrentTime())
        activeClips.forEach((clip) => {
          try {
            // 尝试从视频元素创建媒体流
            if (clip.videoElement && this.hasAudioTrack(clip.videoElement)) {
              // 使用captureStream获取视频元素的流
              const stream =
                (clip.videoElement as any).captureStream?.() ||
                (clip.videoElement as any).mozCaptureStream?.()

              if (stream) {
                const tracks = stream.getAudioTracks()
                audioTracks.push(...tracks)
              }
            }
          } catch (error) {}
        })
      })
    } catch (error) {}

    return audioTracks
  }

  /**
   * 简单直接音频捕获（完全绕过AudioContext）
   */
  private getSimpleDirectAudio(): MediaStream | null {
    try {
      const audioTracks: MediaStreamTrack[] = []

      // 获取当前活跃的视频片段
      this.videoTracks.forEach((track) => {
        const activeClips = track.getActiveClips(this.getCurrentTime())
        activeClips.forEach((clip) => {
          if (clip.videoElement) {
            // 确保视频元素音频设置正确
            clip.videoElement.muted = false
            clip.videoElement.volume = 1.0

            // 确保视频正在播放
            if (clip.videoElement.paused) {
              clip.videoElement.play().catch((error) => {})
            }

            // 直接从视频元素获取音频轨道
            try {
              // 使用captureStream获取完整的媒体流
              let mediaStream: MediaStream | null = null

              if (typeof (clip.videoElement as any).captureStream === 'function') {
                mediaStream = (clip.videoElement as any).captureStream()
              } else if (typeof (clip.videoElement as any).mozCaptureStream === 'function') {
                mediaStream = (clip.videoElement as any).mozCaptureStream()
              }

              if (mediaStream) {
                const videoAudioTracks = mediaStream.getAudioTracks()
                audioTracks.push(...videoAudioTracks)
              } else {
              }
            } catch (error) {}
          }
        })
      })

      if (audioTracks.length > 0) {
        const audioStream = new MediaStream(audioTracks)
        return audioStream
      } else {
        return null
      }
    } catch (error) {
      return null
    }
  }

  /**
   * 直接从视频元素获取音频流（备选方案）
   */
  private getDirectVideoAudioStream(): MediaStream | null {
    try {
      const audioTracks: MediaStreamTrack[] = []

      // 遍历所有活跃的视频片段
      this.videoTracks.forEach((track) => {
        const activeClips = track.getActiveClips(this.getCurrentTime())
        activeClips.forEach((clip) => {
          try {
            // 确保视频元素未静音且正在播放
            if (clip.videoElement) {
              clip.videoElement.muted = false
              clip.videoElement.volume = 1.0

              // 尝试使用captureStream获取音频
              if (typeof (clip.videoElement as any).captureStream === 'function') {
                const stream = (clip.videoElement as any).captureStream()
                const videoAudioTracks = stream.getAudioTracks()
                audioTracks.push(...videoAudioTracks)
              } else if (typeof (clip.videoElement as any).mozCaptureStream === 'function') {
                const stream = (clip.videoElement as any).mozCaptureStream()
                const videoAudioTracks = stream.getAudioTracks()
                audioTracks.push(...videoAudioTracks)
              }
            }
          } catch (error) {}
        })
      })

      if (audioTracks.length > 0) {
        const audioStream = new MediaStream(audioTracks)
        return audioStream
      }

      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 获取所有视频的音频流（改进的多视频支持）
   */
  private getAllVideoAudioStream(): MediaStream | null {
    try {
      const audioTracks: MediaStreamTrack[] = []

      // 遍历所有轨道，收集所有视频的音频（不仅仅是当前活跃的）
      for (const track of this.videoTracks.values()) {
        const allClips = track.getAllClips()
        for (const clip of allClips) {
          if (clip.videoElement && typeof (clip.videoElement as any).captureStream === 'function') {
            try {
              // 确保视频未静音
              clip.videoElement.muted = false

              const stream = (clip.videoElement as any).captureStream()
              const videoAudioTracks = stream.getAudioTracks()

              audioTracks.push(...videoAudioTracks)
            } catch (error) {}
          }
        }
      }

      if (audioTracks.length > 0) {
        return new MediaStream(audioTracks)
      }

      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 获取简单的音频流（回退方案）
   */
  private getSimpleAudioStream(): MediaStream | null {
    try {
      const audioTracks: MediaStreamTrack[] = []

      // 遍历所有轨道，收集当前活跃视频的音频
      for (const track of this.videoTracks.values()) {
        const activeClips = track.getActiveClips(this.getCurrentTime())
        for (const clip of activeClips) {
          if (clip.videoElement && typeof (clip.videoElement as any).captureStream === 'function') {
            try {
              const stream = (clip.videoElement as any).captureStream()
              const videoAudioTracks = stream.getAudioTracks()
              audioTracks.push(...videoAudioTracks)
            } catch (error) {
              // 忽略单个视频的错误，继续处理其他视频
            }
          }
        }
      }

      if (audioTracks.length > 0) {
        return new MediaStream(audioTracks)
      }

      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 创建实时音频混合器，支持多视频动态音频切换
   */
  private createRealTimeAudioMixer(): MediaStream | null {
    try {
      const audioContext = new AudioContext({ sampleRate: 48000 })
      const masterGain = audioContext.createGain()
      const destination = audioContext.createMediaStreamDestination()

      masterGain.connect(destination)

      // 为每个视频片段创建音频源
      const audioSources: Map<string, { source: MediaElementAudioSourceNode; gain: GainNode }> =
        new Map()

      for (const track of this.videoTracks.values()) {
        const allClips = track.getAllClips()

        for (const clip of allClips) {
          if (clip.videoElement) {
            try {
              // 关键修复：在创建音频源之前，确保视频元素不是静音的
              clip.videoElement.muted = false // 先取消静音
              clip.videoElement.volume = 1.0 // 设置音量

              // 创建音频源节点
              const source = audioContext.createMediaElementSource(clip.videoElement)

              // 创建音频源后，再将视频元素静音（避免重复播放音频）
              clip.videoElement.muted = true
              const gainNode = audioContext.createGain()

              // 连接音频链
              source.connect(gainNode)
              gainNode.connect(masterGain)

              // 初始设为静音，由控制逻辑决定何时播放
              gainNode.gain.value = 0

              audioSources.set(clip.id, { source, gain: gainNode })

              // 测试：临时启用音频看看是否有数据
              setTimeout(() => {
                gainNode.gain.value = 1.0 // 临时启用音频
                setTimeout(() => {
                  gainNode.gain.value = 0 // 1秒后关闭
                }, 1000)
              }, 500)
            } catch (error) {}
          } else {
          }
        }
      }

      // 优先启用轨道2的音频源（轨道1通常没有音频）

      setTimeout(() => {
        for (const [clipId, { gain }] of audioSources) {
          // 检查这个clip是否属于轨道2
          let isTrack2Clip = false
          const track2 = this.videoTracks.get('track-2')
          if (track2) {
            const clip = track2.findClipById(clipId)
            if (clip) {
              isTrack2Clip = true
            }
          }

          if (isTrack2Clip) {
            gain.gain.setValueAtTime(1.0, audioContext.currentTime)
          } else {
            gain.gain.setValueAtTime(0.0, audioContext.currentTime)
          }
        }
      }, 100)

      // 创建控制器来动态调整音频
      const updateAudioMix = () => {
        const currentTime = this.getCurrentTime()
        const activeAudioClips: string[] = []

        // 遍历所有音频源，根据当前时间决定是否播放
        for (const [clipId, { gain }] of audioSources) {
          // 找到对应的视频片段
          let clipData: any = null
          for (const track of this.videoTracks.values()) {
            const clip = track.findClipById(clipId)
            if (clip) {
              clipData = clip
              break
            }
          }

          if (clipData) {
            const isActive =
              currentTime >= clipData.startTime &&
              currentTime < clipData.startTime + clipData.duration

            if (isActive) {
              activeAudioClips.push(clipId)

              // 计算相对时间并设置视频播放位置
              const relativeTime = currentTime - clipData.startTime
              const videoTime = clipData.trimStart + relativeTime

              if (
                clipData.videoElement.paused ||
                Math.abs(clipData.videoElement.currentTime - videoTime) > 0.2
              ) {
                clipData.videoElement.currentTime = videoTime
                if (clipData.videoElement.paused) {
                  clipData.videoElement.play().catch(() => {})
                }
              }

              // 设置音频增益 - 只启用轨道2的音频
              let targetGain = 0 // 默认静音

              // 检查这个clip是否属于轨道2
              const track2 = this.videoTracks.get('track-2')
              if (track2 && track2.findClipById(clipId)) {
                // 轨道2的音频，使用正常音量
                targetGain = clipData.muted ? 0 : clipData.volume
              }
              // 其他轨道的音频保持静音（targetGain = 0）

              if (Math.abs(gain.gain.value - targetGain) > 0.01) {
                gain.gain.value = targetGain
                if (targetGain > 0) {
                } else {
                }
              }
            } else {
              // 非活跃片段静音
              if (gain.gain.value > 0) {
                gain.gain.value = 0
              }
              if (!clipData.videoElement.paused) {
                clipData.videoElement.pause()
              }
            }
          }
        }
      }

      // 存储更新函数以便在录制期间调用
      ;(this as any).audioMixerUpdate = updateAudioMix

      // 初始更新
      updateAudioMix()

      return destination.stream
    } catch (error) {
      return null
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成片段名称（保持原文件名并添加片段编号）
   */
  private getSegmentName(originalName?: string, segmentNumber?: number): string {
    if (!originalName) {
      return segmentNumber ? `片段${segmentNumber}` : '未命名片段'
    }

    // 移除文件扩展名
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')

    if (segmentNumber) {
      return `${nameWithoutExt}-片段${segmentNumber}`
    }

    return nameWithoutExt
  }

  /**
   * 生成定格帧名称（基于原文件名+序号）
   */
  private getFreezeFrameName(originalName?: string): string {
    if (!originalName) {
      return '定格帧1'
    }

    // 移除文件扩展名
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')

    // 查找该文件的定格帧数量来确定序号
    let freezeFrameCount = 0
    for (const track of this.videoTracks.values()) {
      const clips = track.getAllClips()
      for (const clip of clips) {
        const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame
        if (isFreezeFrame && clip.name && clip.name.startsWith(nameWithoutExt)) {
          freezeFrameCount++
        }
      }
    }

    return `${nameWithoutExt}${freezeFrameCount + 1}`
  }

  /**
   * 通知状态变化
   */
  private notifyStateChange(): void {
    if (this.onStateChangeCallback) {
      const state = this.getState()
      this.onStateChangeCallback(state)
    }
  }

  /**
   * 公共方法：通知状态变化（用于UI层调用）
   */
  public updateState(): void {
    this.notifyStateChange()
  }

  /**
   * 获取编辑器状态
   */
  public getState(): EditorState {
    // 查找正在播放的轨道或有活跃内容的轨道
    let isPlaying = false
    let currentTime = 0
    let playbackRate = 1.0

    // 优先使用正在播放的轨道状态
    for (const [trackId, track] of this.videoTracks) {
      const trackState = track.getState()
      if (trackState.isPlaying) {
        isPlaying = true
        currentTime = trackState.currentTime
        playbackRate = trackState.playbackRate
        break
      }
    }

    // 如果没有轨道在播放，使用有内容的轨道状态
    if (!isPlaying) {
      for (const [trackId, track] of this.videoTracks) {
        const trackState = track.getState()
        const hasContent = track.getAllClips().length > 0
        if (hasContent) {
          currentTime = trackState.currentTime
          playbackRate = trackState.playbackRate
          break
        }
      }
    }

    return {
      currentTime: currentTime,
      isPlaying: isPlaying,
      playbackRate: playbackRate,
      duration: this.getTotalDuration(),
      volume: 1.0, // 可以从音频混合器获取
    }
  }

  /**
   * 设置状态变化回调
   */
  public onStateChange(callback: (state: EditorState) => void): void {
    this.onStateChangeCallback = callback
  }

  /**
   * 设置时间更新回调
   */
  public onTimeUpdate(callback: (time: number) => void): void {
    this.onTimeUpdateCallback = callback
  }

  /**
   * 调整画布尺寸
   */
  public resize(width: number, height: number): void {
    this.width = width
    this.height = height
    this.renderer.resize(width, height)
  }

  /**
   * 销毁编辑器
   */
  public destroy(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
    }

    // 停止录制
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop()
    }

    // 清理录制流
    if (this.recordingStream) {
      this.recordingStream.getTracks().forEach((track) => track.stop())
    }

    // 销毁各个组件
    this.renderer.destroy()
    this.videoTracks.forEach((track) => track.destroy())
    this.audioMixer.destroy()
  }

  /**
   * 创建定格帧
   */
  public async createFreezeFrame(clipId: string, freezeTime?: number): Promise<string> {
    // 在所有轨道中查找片段
    let sourceClip: VideoClip | null = null
    let sourceTrackId: string | null = null

    for (const [trackId, track] of this.videoTracks) {
      const clip = track.findClipById(clipId)
      if (clip) {
        sourceClip = clip
        sourceTrackId = trackId
        break
      }
    }

    if (!sourceClip) {
      this.videoTracks.forEach((track, trackId) => {
        const clips = track.getAllClips()
      })
      throw new Error('Source clip not found')
    }

    // 使用当前播放时间或指定时间作为定格时间
    const currentTime = freezeTime ?? this.getCurrentTime()

    // 确保定格时间在源片段的时间轴范围内
    if (
      currentTime < sourceClip.startTime ||
      currentTime >= sourceClip.startTime + sourceClip.duration
    ) {
      throw new Error('Freeze time is outside source clip timeline range')
    }

    // 计算在原视频中的绝对时间
    const relativeTimeInClip = currentTime - sourceClip.startTime
    const videoTime = sourceClip.trimStart + relativeTimeInClip

    // 确保视频时间在trim范围内
    if (videoTime < sourceClip.trimStart || videoTime > sourceClip.trimEnd) {
      throw new Error('Freeze time is outside clip trim range')
    }

    // 创建画布来捕获当前帧
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to create canvas context')
    }

    // 设置画布尺寸
    canvas.width = sourceClip.videoElement.videoWidth || 1920
    canvas.height = sourceClip.videoElement.videoHeight || 1080

    return new Promise((resolve, reject) => {
      const targetTime = videoTime
      const currentVideoTime = sourceClip.videoElement.currentTime

      let seekCompleted = false
      let timeoutId: number

      const drawFrame = () => {
        try {
          ctx.drawImage(sourceClip!.videoElement, 0, 0, canvas.width, canvas.height)

          // 转换为Blob并创建图片
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('Failed to create freeze frame blob'))
              return
            }

            const objectUrl = URL.createObjectURL(blob)
            const img = new Image()

            img.onload = async () => {
              const freezeClipId = this.generateId()

              // 创建定格片段（使用图片作为视频源）
              const freezeClip: VideoClip = {
                id: freezeClipId,
                videoElement: this.createVideoFromImage(img),
                name: this.getFreezeFrameName(sourceClip!.name), // 使用文件名+序号
                startTime: currentTime,
                duration: 3.0, // 默认3秒
                trimStart: 0,
                trimEnd: 3.0,
                opacity: 1.0,
                volume: 0, // 定格帧没有音频
                muted: true,
                transform: { ...sourceClip!.transform },
                effects: [],
              }

              // 插入定格帧到视频中（切开原视频）
              const track = this.videoTracks.get(sourceTrackId!)
              if (track) {
                await this.insertFreezeFrameIntoVideo(track, sourceClip!, freezeClip, currentTime)
              }

              this.notifyStateChange()
              resolve(freezeClipId)
            }

            img.onerror = () => {
              URL.revokeObjectURL(objectUrl)
              reject(new Error('Failed to load freeze frame image'))
            }

            img.src = objectUrl
          }, 'image/png')
        } catch (error) {
          reject(error)
        }
      }

      // 如果时间差很小，直接绘制
      if (Math.abs(currentVideoTime - targetTime) < 0.033) {
        // 约1帧的时间

        drawFrame()
        return
      }

      const onSeeked = () => {
        if (seekCompleted) return
        seekCompleted = true

        sourceClip!.videoElement.removeEventListener('seeked', onSeeked)
        clearTimeout(timeoutId)

        // 等待一帧时间确保画面更新
        setTimeout(() => {
          drawFrame()
        }, 33) // 约1帧时间
      }

      const onTimeUpdate = () => {
        const actualTime = sourceClip!.videoElement.currentTime
        if (Math.abs(actualTime - targetTime) < 0.033) {
          sourceClip!.videoElement.removeEventListener('timeupdate', onTimeUpdate)
          onSeeked()
        }
      }

      // 设置超时保护
      timeoutId = setTimeout(() => {
        if (seekCompleted) return
        seekCompleted = true

        sourceClip!.videoElement.removeEventListener('seeked', onSeeked)
        sourceClip!.videoElement.removeEventListener('timeupdate', onTimeUpdate)
        drawFrame()
      }, 2000) // 2秒超时

      // 监听seeked和timeupdate事件
      sourceClip.videoElement.addEventListener('seeked', onSeeked, { once: true })
      sourceClip.videoElement.addEventListener('timeupdate', onTimeUpdate)

      // 设置视频时间
      sourceClip.videoElement.currentTime = targetTime
    })
  }

  /**
   * 创建从图片生成的视频元素（用于定格帧）
   */
  private createVideoFromImage(img: HTMLImageElement): HTMLVideoElement {
    // 创建一个特殊的视频元素，用于显示静态图片
    const video = document.createElement('video')
    video.width = img.width
    video.height = img.height
    video.muted = true
    video.loop = true
    video.autoplay = false

    // 设置视频属性，让它看起来像一个有效的视频元素
    Object.defineProperty(video, 'videoWidth', { value: img.width, writable: false })
    Object.defineProperty(video, 'videoHeight', { value: img.height, writable: false })
    Object.defineProperty(video, 'readyState', { value: 4, writable: false }) // HAVE_ENOUGH_DATA
    Object.defineProperty(video, 'duration', { value: Infinity, writable: false })
    Object.defineProperty(video, 'currentTime', { value: 0, writable: true })

    // 生成唯一的定格帧ID，避免缓存冲突
    const freezeFrameId = `freeze_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`

    // 存储原始图片引用，用于渲染
    ;(video as any)._freezeFrameImage = img
    ;(video as any)._isFreezeFrame = true
    ;(video as any)._freezeFrameId = freezeFrameId

    return video
  }

  /**
   * 对齐到视频的实际帧时间，避免切在帧间隙或黑色帧上
   */
  private async alignToVideoFrame(
    videoElement: HTMLVideoElement,
    targetTime: number,
  ): Promise<number> {
    return new Promise((resolve) => {
      // 设置目标时间
      videoElement.currentTime = targetTime

      let attempts = 0
      const maxAttempts = 10

      const checkFrame = () => {
        attempts++

        // 创建临时canvas检查当前帧是否为有效图像
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          resolve(targetTime)
          return
        }

        canvas.width = 100 // 小尺寸用于快速检测
        canvas.height = 100

        try {
          // 绘制当前帧
          ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)

          // 获取像素数据检查是否为黑色帧
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          const data = imageData.data

          let totalBrightness = 0
          let pixelCount = 0

          // 计算平均亮度
          for (let i = 0; i < data.length; i += 4) {
            const r = data[i]
            const g = data[i + 1]
            const b = data[i + 2]
            const brightness = (r + g + b) / 3
            totalBrightness += brightness
            pixelCount++
          }

          const avgBrightness = totalBrightness / pixelCount

          // 如果平均亮度太低（可能是黑色帧），微调时间
          if (avgBrightness < 10 && attempts < maxAttempts) {
            // 向前微调一帧的时间（假设30fps）
            const frameTime = 1 / 30
            videoElement.currentTime = targetTime + attempts * frameTime * 0.1

            // 等待时间更新后再次检查
            setTimeout(checkFrame, 50)
            return
          }

          // 当前时间为有效帧时间
          resolve(videoElement.currentTime)
        } catch (error) {
          resolve(targetTime)
        }
      }

      // 等待时间设置生效
      setTimeout(checkFrame, 100)
    })
  }

  /**
   * 创建精确的视频片段，避免时间同步问题
   */
  private async createPreciseVideoSegment(
    originalVideo: HTMLVideoElement,
    startTime: number,
    endTime: number,
  ): Promise<HTMLVideoElement> {
    // 创建新的video元素，完全独立
    const newVideo = document.createElement('video')

    try {
      // 使用Blob URL确保独立性
      const response = await fetch(originalVideo.src)
      const blob = await response.blob()
      const newBlobUrl = URL.createObjectURL(blob)

      newVideo.src = newBlobUrl
      newVideo.preload = 'metadata'
      newVideo.crossOrigin = 'anonymous'

      // 复制基本属性
      newVideo.muted = originalVideo.muted
      newVideo.volume = originalVideo.volume
      newVideo.playbackRate = originalVideo.playbackRate

      // 等待元数据加载
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Video metadata loading timeout'))
        }, 5000)

        newVideo.addEventListener(
          'loadedmetadata',
          () => {
            clearTimeout(timeout)
            resolve()
          },
          { once: true },
        )

        newVideo.addEventListener(
          'error',
          () => {
            clearTimeout(timeout)
            reject(new Error('Video loading error'))
          },
          { once: true },
        )
      })

      // 设置精确的开始时间
      newVideo.currentTime = startTime

      // 等待时间设置完成
      await new Promise<void>((resolve) => {
        const checkTime = () => {
          if (Math.abs(newVideo.currentTime - startTime) < 0.1) {
            resolve()
          } else {
            setTimeout(checkTime, 50)
          }
        }
        checkTime()
      })

      // 添加时间控制，防止播放超出范围
      newVideo.addEventListener('timeupdate', () => {
        if (newVideo.currentTime >= endTime) {
          newVideo.pause()
          newVideo.currentTime = endTime
        }
      })

      // 标记为精确片段
      ;(newVideo as any)._isPreciseSegment = true
      ;(newVideo as any)._segmentStart = startTime
      ;(newVideo as any)._segmentEnd = endTime
      ;(newVideo as any)._originalSrc = originalVideo.src

      return newVideo
    } catch (error) {
      // 降级到原有的克隆方法
      return this.fallbackCloneVideoElement(originalVideo)
    }
  }

  /**
   * 降级的克隆视频元素方法（备用）
   */
  private fallbackCloneVideoElement(originalVideo: HTMLVideoElement): HTMLVideoElement {
    const clonedVideo = document.createElement('video')

    // 复制基本属性
    clonedVideo.src = originalVideo.src
    clonedVideo.width = originalVideo.width
    clonedVideo.height = originalVideo.height
    clonedVideo.muted = originalVideo.muted
    clonedVideo.volume = originalVideo.volume
    clonedVideo.playbackRate = originalVideo.playbackRate
    clonedVideo.preload = 'auto' // 切割后的片段需要积极预加载

    // 智能处理crossOrigin属性：只有在原视频确实需要CORS时才设置
    if (originalVideo.crossOrigin && originalVideo.src.startsWith('http')) {
      clonedVideo.crossOrigin = originalVideo.crossOrigin
    }

    // 重要：设置初始时间为原视频的当前时间，确保克隆视频有正确的起始状态
    clonedVideo.currentTime = originalVideo.currentTime

    // 添加预加载优化：立即开始加载，但使用更智能的策略
    clonedVideo.load()

    // 添加积极的预加载监听
    const ensurePreloaded = () => {
      if (clonedVideo.readyState < clonedVideo.HAVE_CURRENT_DATA) {
        // 如果数据不足，再次尝试加载
        setTimeout(() => {
          clonedVideo.load()
        }, 100)
      }
    }

    // 在多个时机检查预加载状态
    clonedVideo.addEventListener('loadstart', ensurePreloaded, { once: true })
    clonedVideo.addEventListener('progress', ensurePreloaded, { once: true })

    // 延迟检查，确保有足够数据
    setTimeout(ensurePreloaded, 200)
    setTimeout(ensurePreloaded, 500)

    // 同步加载状态：等待原视频加载完成后再设置克隆视频的属性
    const setupClonedVideo = () => {
      // 复制只读属性（通过defineProperty）
      Object.defineProperty(clonedVideo, 'videoWidth', {
        value: originalVideo.videoWidth,
        writable: false,
      })
      Object.defineProperty(clonedVideo, 'videoHeight', {
        value: originalVideo.videoHeight,
        writable: false,
      })
    }

    // 如果原视频已经加载完成，直接设置属性
    if (originalVideo.readyState >= originalVideo.HAVE_METADATA) {
      setupClonedVideo()
    } else {
      // 等待原视频加载完成
      originalVideo.addEventListener('loadedmetadata', setupClonedVideo, { once: true })
    }

    // 添加时间同步监听，确保克隆视频能正确响应时间变化
    clonedVideo.addEventListener('seeked', () => {})

    // 标记这是一个克隆的视频元素，用于调试
    ;(clonedVideo as any)._isClonedVideo = true
    ;(clonedVideo as any)._originalSrc = originalVideo.src

    // 添加错误监听和重试机制
    clonedVideo.addEventListener('error', (e) => {
      // 如果是CORS错误，尝试不使用crossOrigin重新加载
      if (clonedVideo.error?.code === MediaError.MEDIA_ERR_NETWORK && clonedVideo.crossOrigin) {
        clonedVideo.crossOrigin = null
        setTimeout(() => {
          clonedVideo.load()
        }, 100)
      }
    })

    // 防止频闪：为克隆视频添加标记，避免过度时间同步
    ;(clonedVideo as any)._isCloned = true
    ;(clonedVideo as any)._cloneTimestamp = Date.now()

    return clonedVideo
  }

  /**
   * 将定格帧插入到视频中（切开原视频）
   */
  private async insertFreezeFrameIntoVideo(
    track: any,
    sourceClip: VideoClip,
    freezeClip: VideoClip,
    insertTime: number,
  ): Promise<void> {
    // 计算插入点在源片段中的相对时间
    const relativeInsertTime = insertTime - sourceClip.startTime

    // 如果插入点不在源片段范围内，直接添加定格帧
    if (relativeInsertTime < 0 || relativeInsertTime >= sourceClip.duration) {
      track.addClip(freezeClip)
      return
    }

    // 如果插入点在片段开始处（误差范围内）
    if (relativeInsertTime <= 0.1) {
      // 调整原片段：向后移动，保持其他属性不变
      const adjustedSourceVideo = await this.createPreciseVideoSegment(
        sourceClip.videoElement,
        sourceClip.trimStart,
        sourceClip.trimEnd,
      )
      const adjustedSourceClip: VideoClip = {
        ...sourceClip,
        id: this.generateId(),
        videoElement: adjustedSourceVideo,
        name: this.getSegmentName(sourceClip.name, 1), // 保持原名称
        startTime: freezeClip.startTime + freezeClip.duration,
      }

      // 移除原片段，添加定格帧和调整后的源片段
      track.removeClip(sourceClip.id)
      track.addClip(freezeClip)
      track.addClip(adjustedSourceClip)
      return
    }

    // 如果插入点在片段结束处（误差范围内）
    if (relativeInsertTime >= sourceClip.duration - 0.1) {
      // 保持原片段不变，只是克隆视频元素
      const clonedSourceVideo = await this.createPreciseVideoSegment(
        sourceClip.videoElement,
        sourceClip.trimStart,
        sourceClip.trimEnd,
      )
      const clonedSourceClip: VideoClip = {
        ...sourceClip,
        id: this.generateId(),
        videoElement: clonedSourceVideo,
        name: this.getSegmentName(sourceClip.name, 1), // 保持原名称
      }

      // 定格帧紧接在原片段后面
      freezeClip.startTime = sourceClip.startTime + sourceClip.duration

      // 移除原片段，添加克隆片段和定格帧
      track.removeClip(sourceClip.id)
      track.addClip(clonedSourceClip)
      track.addClip(freezeClip)
      return
    }

    // 在片段中间插入，需要切开原片段
    // 计算在原视频中的实际切分时间点
    const videoSplitTime = sourceClip.trimStart + relativeInsertTime

    // 创建前半段片段
    const firstPartVideo = await this.createPreciseVideoSegment(
      sourceClip.videoElement,
      sourceClip.trimStart,
      videoSplitTime,
    )
    const firstPart: VideoClip = {
      ...sourceClip,
      id: this.generateId(),
      videoElement: firstPartVideo,
      name: this.getSegmentName(sourceClip.name, 1), // 文件名-片段1
      // 前半段：从原开始时间到插入点
      startTime: sourceClip.startTime,
      duration: relativeInsertTime,
      trimStart: sourceClip.trimStart,
      trimEnd: videoSplitTime,
    }

    // 修正定格帧的startTime：应该紧接在前半段之后
    freezeClip.startTime = sourceClip.startTime + relativeInsertTime

    // 重新计算后半段的trimStart，确保精确性
    const originalVideoStartTime = sourceClip.trimStart
    const originalVideoEndTime = sourceClip.trimEnd
    const splitPointInOriginalVideo = originalVideoStartTime + relativeInsertTime

    // 后半段应该从分割点开始播放到原视频结束
    const secondPartTrimStart = splitPointInOriginalVideo
    const secondPartTrimEnd = originalVideoEndTime
    const secondPartDuration = secondPartTrimEnd - secondPartTrimStart

    // 创建后半段片段 - 使用绝对时间计算，避免累积误差
    const secondPartVideo = await this.createPreciseVideoSegment(
      sourceClip.videoElement,
      secondPartTrimStart,
      secondPartTrimEnd,
    )

    const secondPart: VideoClip = {
      ...sourceClip,
      id: this.generateId(),
      videoElement: secondPartVideo,
      name: this.getSegmentName(sourceClip.name, 2), // 文件名-片段2
      // 后半段：直接基于定格帧的结束时间
      startTime: freezeClip.startTime + freezeClip.duration,
      duration: secondPartDuration, // 使用计算出的精确时长
      trimStart: secondPartTrimStart, // 使用重新计算的trimStart
      trimEnd: secondPartTrimEnd, // 使用原始的trimEnd
    }

    // 验证后半段的时间设置
    if (
      secondPart.trimStart < 0 ||
      secondPart.trimStart >= secondPart.trimEnd ||
      secondPart.duration <= 0
    ) {
      secondPart.trimStart = Math.max(0, originalVideoStartTime + relativeInsertTime)
      secondPart.trimEnd = originalVideoEndTime
      secondPart.duration = Math.max(0.1, secondPart.trimEnd - secondPart.trimStart)
    }

    // 移除原片段，按顺序添加：前半段 -> 定格帧 -> 后半段
    track.removeClip(sourceClip.id)
    track.addClip(firstPart)
    track.addClip(freezeClip)
    track.addClip(secondPart)

    // 获取轨道ID，根据轨道类型选择处理策略
    const currentTrackId = this.getTrackId(track)
    if (currentTrackId === 'track-1') {
      // 轨道一：只使用重新排列，不调用adjustSubsequentClipsTime（避免重复调整）

      this.rearrangeTrackClips(currentTrackId)
    } else {
      // 其他轨道：调整后续片段时间

      this.adjustSubsequentClipsTime(track, insertTime, freezeClip.duration)
    }

    // 强制刷新视频轨道，确保克隆的视频片段正确显示

    setTimeout(() => {
      // 强制重置所有克隆视频的状态
      this.resetAllClonedVideoStates(track)

      // 强制同步所有片段的时间，特别是克隆片段
      this.synchronizeClonedVideoTimes(track)

      // 强制更新轨道时间，确保所有片段正确同步
      const currentTime = this.getCurrentTime()
      track.updateVideoElements(currentTime)

      // 强制渲染当前帧
      this.renderFrame()
    }, 100) // 短暂延迟，让片段添加完成
  }

  /**
   * 重置所有克隆视频的状态，彻底解决累积错误
   */
  private resetAllClonedVideoStates(track: any): void {
    const allClips = track.getAllClips()

    allClips.forEach((clip: any) => {
      const isClonedVideo = (clip.videoElement as any)._isClonedVideo
      if (isClonedVideo) {
        // 强制重新加载视频元素，清除所有内部状态
        const originalSrc = clip.videoElement.src
        const originalPreload = clip.videoElement.preload

        // 暂停并重置
        clip.videoElement.pause()
        clip.videoElement.currentTime = 0

        // 清除src并重新设置，强制重新加载
        clip.videoElement.removeAttribute('src')
        clip.videoElement.load()

        // 重新设置src和属性
        setTimeout(() => {
          clip.videoElement.src = originalSrc
          clip.videoElement.preload = originalPreload || 'auto'
          clip.videoElement.load()

          // 设置到正确的开始时间
          setTimeout(() => {
            clip.videoElement.currentTime = clip.trimStart
          }, 100)
        }, 50)

        // 清除所有时间同步标记
        delete (clip.videoElement as any)._lastSyncTime
        delete (clip.videoElement as any)._pendingSeek
      }
    })
  }

  /**
   * 同步克隆视频的时间，确保时间计算准确
   */
  private synchronizeClonedVideoTimes(track: any): void {
    const allClips = track.getAllClips()
    const currentTime = this.getCurrentTime()

    allClips.forEach((clip: any) => {
      const isClonedVideo = (clip.videoElement as any)._isClonedVideo
      if (isClonedVideo) {
        // 重新计算克隆视频的精确时间
        const relativeTime = currentTime - clip.startTime

        // 确保相对时间在有效范围内
        if (relativeTime < 0 || relativeTime > clip.duration) {
          return
        }

        const videoTime = clip.trimStart + relativeTime
        const maxVideoTime = clip.trimEnd || clip.trimStart + clip.duration
        const clampedVideoTime = Math.max(clip.trimStart, Math.min(videoTime, maxVideoTime))

        // 验证计算出的时间是否合理
        if (clampedVideoTime < 0 || isNaN(clampedVideoTime)) {
          clip.videoElement.currentTime = clip.trimStart
        } else {
          clip.videoElement.currentTime = clampedVideoTime
        }

        // 标记为已同步，避免后续的自动调整
        ;(clip.videoElement as any)._lastSyncTime = Date.now()
      }
    })
  }

  /**
   * 调整后续片段的时间（当插入定格帧时）
   */
  private adjustSubsequentClipsTime(track: any, insertTime: number, offsetDuration: number): void {
    const allClips = track.getAllClips()

    // 找到所有在原插入点之后的片段（不包括刚插入的定格帧相关片段）
    // 使用原始的插入时间，而不是插入时间+偏移
    const subsequentClips = allClips.filter((clip: VideoClip) => {
      // 排除定格帧和刚创建的片段
      const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame
      const isNewlyCreated = Date.now() - ((clip.videoElement as any)._cloneTimestamp || 0) < 1000

      return clip.startTime > insertTime && !isFreezeFrame && !isNewlyCreated
    })

    // 将这些片段向后推移
    subsequentClips.forEach((clip: VideoClip) => {
      const newStartTime = clip.startTime + offsetDuration
      track.updateClip(clip.id, { startTime: newStartTime })
    })
  }

  /**
   * 获取轨道ID
   */
  private getTrackId(track: any): string | null {
    for (const [trackId, t] of this.videoTracks) {
      if (t === track) {
        return trackId
      }
    }
    return null
  }

  /**
   * 重新排列轨道片段，消除空白间隙
   */
  private rearrangeTrackClips(trackId: string): void {
    const track = this.videoTracks.get(trackId)
    if (!track) {
      return
    }

    const allClips = track.getAllClips()
    if (allClips.length <= 1) {
      return
    }

    // 按开始时间排序
    allClips.sort((a: any, b: any) => a.startTime - b.startTime)

    // 记录原始位置
    const originalPositions = allClips.map((clip: any) => ({
      id: clip.id.slice(-6),
      startTime: clip.startTime.toFixed(3),
      duration: clip.duration.toFixed(3),
    }))

    // 重新排列，消除空白
    let currentTime = 0
    const rearrangedClips: any[] = []

    for (const clip of allClips) {
      // 创建新的片段副本，更新开始时间
      const newClip = {
        ...clip,
        startTime: currentTime,
      }

      rearrangedClips.push(newClip)
      currentTime += clip.duration
    }

    // 清空轨道并重新添加排列后的片段
    // 先移除所有现有片段
    const clipIds = allClips.map((clip: any) => clip.id)
    clipIds.forEach((clipId) => {
      track.removeClip(clipId)
    })

    // 重新添加排列后的片段
    rearrangedClips.forEach((clip) => {
      track.addClip(clip)
    })

    // 记录新位置
    const newPositions = rearrangedClips.map((clip: any) => ({
      id: clip.id.slice(-6),
      startTime: clip.startTime.toFixed(3),
      duration: clip.duration.toFixed(3),
    }))
  }

  /**
   * 调整片段拉伸后的后续片段位置
   */
  private adjustSubsequentClipsAfterResize(
    track: any,
    resizedClip: any,
    durationChange: number,
  ): void {
    const allClips = track.getAllClips()

    // 计算拉伸前的结束时间
    const originalEndTime = resizedClip.startTime + (resizedClip.duration - durationChange)

    // 找到需要调整的后续片段（开始时间在原结束时间之后的）
    const subsequentClips = allClips.filter((clip: any) => {
      const isAfterOriginalEnd = clip.startTime >= originalEndTime - 0.001 // 1ms容差
      const isNotSameClip = clip.id !== resizedClip.id

      return isNotSameClip && isAfterOriginalEnd
    })

    if (subsequentClips.length === 0) {
      return
    }

    // 按开始时间排序
    subsequentClips.sort((a: any, b: any) => a.startTime - b.startTime)

    // 调整后续片段的位置
    const adjustmentOffset = durationChange
    for (const clip of subsequentClips) {
      const oldStartTime = clip.startTime
      clip.startTime = Math.max(0, oldStartTime + adjustmentOffset)
    }
  }

  /**
   * 更新定格帧持续时间
   */
  public updateFreezeFrameDuration(clipId: string, newDuration: number): boolean {
    for (const [trackId, track] of this.videoTracks) {
      const clip = track.findClipById(clipId)
      if (clip) {
        const oldDuration = clip.duration
        const newValidDuration = Math.max(0.1, newDuration)

        clip.duration = newValidDuration
        clip.trimEnd = newValidDuration

        // 检查是否为定格帧
        const isFreezeFrame = (clip.videoElement as any)._isFreezeFrame
        if (isFreezeFrame) {
          // 如果是轨道一的定格帧，调整后续素材位置
          if (trackId === 'track-1') {
            const durationChange = newValidDuration - oldDuration

            // 延迟执行，确保时长更新完成
            setTimeout(() => {
              this.adjustSubsequentClipsAfterResize(track, clip, durationChange)
            }, 100)
          }
        }

        this.notifyStateChange()
        return true
      }
    }

    return false
  }

  /**
   * 创建兼容的MediaRecorder，强制使用非Opus音频格式
   * 现在使用优化的MediaRecorder来改善音频质量
   */
  private createCompatibleMediaRecorder(stream: MediaStream): MediaRecorder {
    // 调试：检查所有可能的格式支持
    const allTypes = [
      'video/mp4;codecs="avc1.42E01E,mp4a.40.2"',
      'video/mp4;codecs="avc1.42001E,mp4a.40.2"',
      'video/mp4;codecs=h264,aac',
      'video/mp4;codecs="h264,aac"',
      'video/webm;codecs="vp8,vorbis"',
      'video/webm;codecs="vp9,vorbis"',
      'video/webm;codecs=vp8,vorbis',
      'video/webm;codecs=vp9,vorbis',
      'video/webm;codecs="h264,aac"',
      'video/webm;codecs=h264,aac',
      'video/webm',
      'video/mp4',
    ]

    allTypes.forEach((type) => {
      const supported = MediaRecorder.isTypeSupported(type)
    })

    // 优先尝试MP4格式，强制使用AAC音频编码（最佳兼容性）
    const mp4AacTypes = [
      'video/mp4;codecs="avc1.42E01E,mp4a.40.2"', // H.264 Baseline + AAC-LC (最兼容)
      'video/mp4;codecs="avc1.42001E,mp4a.40.2"', // H.264 Baseline + AAC-LC (备选)
      'video/mp4;codecs=h264,aac', // 简化格式
      'video/mp4;codecs="h264,aac"', // 引号格式
    ]

    for (const type of mp4AacTypes) {
      if (MediaRecorder.isTypeSupported(type)) {
        const recorder = new MediaRecorder(stream, {
          mimeType: type,
          videoBitsPerSecond: 1500000, // 进一步降低视频比特率，确保稳定性
          audioBitsPerSecond: 96000, // 进一步降低音频比特率，提高稳定性
        })

        return recorder
      }
    }

    // 备选：尝试WebM+Vorbis格式（比Opus兼容性更好）
    const vorbisTypes = [
      'video/webm;codecs="vp8,vorbis"', // VP8 + Vorbis (引号格式)
      'video/webm;codecs="vp9,vorbis"', // VP9 + Vorbis (引号格式)
      'video/webm;codecs=vp8,vorbis', // VP8 + Vorbis (简化格式)
      'video/webm;codecs=vp9,vorbis', // VP9 + Vorbis (简化格式)
    ]

    for (const type of vorbisTypes) {
      if (MediaRecorder.isTypeSupported(type)) {
        const recorder = new MediaRecorder(stream, {
          mimeType: type,
          videoBitsPerSecond: 2500000,
          audioBitsPerSecond: 192000,
        })

        return recorder
      }
    }

    // 检查是否有可用的其他兼容格式（避免Opus）
    const fallbackTypes = [
      'video/webm;codecs="h264,aac"', // WebM + H.264 + AAC
      'video/webm;codecs=h264,aac', // WebM + H.264 + AAC (简化)
    ]

    for (const type of fallbackTypes) {
      if (MediaRecorder.isTypeSupported(type)) {
        return new MediaRecorder(stream, {
          mimeType: type,
          videoBitsPerSecond: 2500000,
          audioBitsPerSecond: 192000,
        })
      }
    }

    // 警告：如果到这里，说明浏览器不支持兼容的音频编码

    // 最后尝试基本MP4格式（但警告可能使用Opus）
    if (MediaRecorder.isTypeSupported('video/mp4')) {
      const recorder = new MediaRecorder(stream, {
        mimeType: 'video/mp4',
        videoBitsPerSecond: 2500000,
        audioBitsPerSecond: 192000,
      })

      return recorder
    }

    // 最后尝试基本webm格式
    if (MediaRecorder.isTypeSupported('video/webm')) {
      return new MediaRecorder(stream, {
        mimeType: 'video/webm',
        videoBitsPerSecond: 2500000,
        audioBitsPerSecond: 192000,
      })
    }

    // 最后的备选方案：使用默认格式，但强制包含音频

    const recorder = new MediaRecorder(stream, {
      videoBitsPerSecond: 2500000,
      audioBitsPerSecond: 192000,
    })

    return recorder
  }

  /**
   * 创建强制Vorbis编码的音频流
   */
  private async createVorbisAudioStream(originalStream: MediaStream): Promise<MediaStream> {
    try {
      // 创建音频上下文
      const audioContext = new AudioContext({ sampleRate: 48000 })

      // 创建音频源
      const source = audioContext.createMediaStreamSource(originalStream)

      // 创建增益节点确保音频处理
      const gainNode = audioContext.createGain()
      gainNode.gain.value = 1.0

      // 创建音频目标
      const destination = audioContext.createMediaStreamDestination()

      // 连接音频处理链
      source.connect(gainNode)
      gainNode.connect(destination)

      // 确保AudioContext运行
      if (audioContext.state === 'suspended') {
        await audioContext.resume()
      }

      return destination.stream
    } catch (error) {
      return originalStream
    }
  }

  /**
   * 创建录制流 - 完全使用预览时的音频逻辑
   */
  private async createCompatibleRecordingStream(): Promise<MediaStream> {
    if (!this.canvas) {
      throw new Error('Canvas not found')
    }

    // 获取视频流
    const videoStream = this.canvas.captureStream(30)

    // 直接使用AudioMixer的输出流（与预览完全一致）
    let audioStream: MediaStream | null = null

    if (this.audioMixer) {
      // 检查AudioMixer状态

      // 强制激活音频轨道用于录制
      this.audioMixer.forceActivateTracksForRecording()

      // 获取AudioMixer的输出流（不做任何额外处理）
      audioStream = this.audioMixer.getOutputStreamForced()

      if (!(audioStream && audioStream.getAudioTracks().length > 0)) {
        // 尝试重新初始化AudioMixer
        try {
          await this.audioMixer.prepareForPlayback(this.getCurrentTime())
          audioStream = this.audioMixer.getOutputStreamForced()

          if (audioStream && audioStream.getAudioTracks().length > 0) {
          } else {
            audioStream = null
          }
        } catch (error) {
          audioStream = null
        }
      }
    }

    // 合并视频和音频流
    const streamTracks: MediaStreamTrack[] = [...videoStream.getVideoTracks()]

    if (audioStream && audioStream.getAudioTracks().length > 0) {
      streamTracks.push(...audioStream.getAudioTracks())
    } else {
    }

    const combinedStream = new MediaStream(streamTracks)
    return combinedStream
  }

  /**
   * 直接从轨道2创建音频流（使用预览时相同的逻辑）
   */
  private async createDirectTrack2AudioStream(): Promise<MediaStream | null> {
    try {
      // 强制AudioMixer进入录制状态，就像预览播放一样
      const currentTime = this.getCurrentTime()
      this.audioMixer.updateTime(currentTime, true) // 设为播放状态

      // 等待音频准备
      await this.audioMixer.prepareForPlayback(currentTime)

      // 强制激活录制轨道
      this.audioMixer.forceActivateTracksForRecording()

      // 再次更新时间确保同步
      this.audioMixer.updateTime(currentTime, true)

      // 等待一下让音频稳定
      await new Promise((resolve) => setTimeout(resolve, 200))

      // 尝试获取AudioMixer的输出流
      let audioStream = this.audioMixer.getOutputStreamForced()

      if (!audioStream || audioStream.getAudioTracks().length === 0) {
        // 备选方案：直接从当前活跃的轨道2视频元素获取音频
        const track2 = this.videoTracks.get('track-2')
        if (track2) {
          const activeClips = track2.getActiveClips(currentTime)
          if (activeClips.length > 0) {
            const activeClip = activeClips[0]
            const videoElement = activeClip.videoElement

            // 确保视频元素音频状态正确
            videoElement.muted = false
            videoElement.volume = 1.0

            // 确保视频在正确时间位置
            const relativeTime = currentTime - activeClip.startTime
            const videoTime = activeClip.trimStart + relativeTime
            if (Math.abs(videoElement.currentTime - videoTime) > 0.1) {
              videoElement.currentTime = videoTime
            }

            // 确保视频正在播放
            if (videoElement.paused) {
              await videoElement.play()
            }

            // 从视频元素获取音频流
            if (typeof (videoElement as any).captureStream === 'function') {
              const mediaStream = (videoElement as any).captureStream()
              const audioTracks = mediaStream.getAudioTracks()
              if (audioTracks.length > 0) {
                audioStream = new MediaStream(audioTracks)

                // 重新静音视频元素避免重复播放
                videoElement.muted = true
              }
            }
          }
        }
      }

      if (audioStream && audioStream.getAudioTracks().length > 0) {
        return audioStream
      } else {
        return null
      }
    } catch (error) {
      return null
    }
  }

  /**
   * 创建基本的轨道2音频流（简化版本，用作备选方案）
   */
  private async createBasicTrack2AudioStream(): Promise<MediaStream | null> {
    try {
      // 获取轨道2
      const track2 = this.videoTracks.get('track-2')
      if (!track2) {
        return null
      }

      // 获取当前活跃的片段
      const currentTime = this.getCurrentTime()
      const activeClips = track2.getActiveClips(currentTime)

      if (activeClips.length === 0) {
        return null
      }

      const activeClip = activeClips[0]
      const videoElement = activeClip.videoElement

      // 确保视频元素准备好
      if (videoElement.readyState < 2) {
        videoElement.load()
        await new Promise((resolve) => {
          const checkReady = () => {
            if (videoElement.readyState >= 2) {
              resolve(undefined)
            } else {
              setTimeout(checkReady, 100)
            }
          }
          checkReady()
        })
      }

      // 设置音频状态
      videoElement.muted = false
      videoElement.volume = 1.0

      // 获取媒体流
      let mediaStream: MediaStream | null = null
      if (typeof (videoElement as any).captureStream === 'function') {
        mediaStream = (videoElement as any).captureStream()
      } else if (typeof (videoElement as any).mozCaptureStream === 'function') {
        mediaStream = (videoElement as any).mozCaptureStream()
      }

      if (mediaStream) {
        const audioTracks = mediaStream.getAudioTracks()
        if (audioTracks.length > 0) {
          // 重新静音视频元素
          videoElement.muted = true
          return new MediaStream(audioTracks)
        } else {
        }
      }

      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 创建动态音频流（能够在轨道内片段切换时自动切换音频源）
   */
  private async createSimpleAudioStream(): Promise<MediaStream | null> {
    try {
      // 创建AudioContext来混合轨道2的所有音频
      const audioContext = new AudioContext({ sampleRate: 48000 })
      const masterGain = audioContext.createGain()
      const destination = audioContext.createMediaStreamDestination()
      masterGain.connect(destination)

      // 获取轨道2的所有视频片段
      const track2 = this.videoTracks.get('track-2')
      if (!track2) {
        return null
      }

      const allClips = track2.getAllClips()
      if (allClips.length === 0) {
        return null
      }

      // 为轨道2的每个视频片段创建音频源
      const audioSources: Map<string, { source: MediaElementAudioSourceNode; gain: GainNode }> =
        new Map()

      for (const clip of allClips) {
        try {
          // 确保视频元素已加载并准备好
          if (clip.videoElement.readyState < 2) {
            clip.videoElement.load()
            // 等待视频加载
            await new Promise((resolve) => {
              const checkReady = () => {
                if (clip.videoElement.readyState >= 2) {
                  resolve(undefined)
                } else {
                  setTimeout(checkReady, 100)
                }
              }
              checkReady()
            })
          }

          // 确保视频元素不是静音的，并设置正确的音量
          const wasMuted = clip.videoElement.muted
          clip.videoElement.muted = false
          clip.videoElement.volume = 1.0

          // 等待一下确保音频准备好
          await new Promise((resolve) => setTimeout(resolve, 50))

          // 创建音频源节点
          const source = audioContext.createMediaElementSource(clip.videoElement)
          const gainNode = audioContext.createGain()

          // 连接音频链
          source.connect(gainNode)
          gainNode.connect(masterGain)

          // 初始设为静音，由控制逻辑决定何时播放
          gainNode.gain.value = 0

          audioSources.set(clip.id, { source, gain: gainNode })
          // 重新静音视频元素（避免重复播放）
          clip.videoElement.muted = true
        } catch (error) {
          // 如果创建音频源失败，至少确保视频元素状态正确
          clip.videoElement.muted = true
        }
      }

      if (audioSources.size === 0) {
        return null
      }

      // 创建动态音频切换控制器
      let lastActiveClipId = ''
      const updateAudioSources = () => {
        const currentTime = this.getCurrentTime()
        const activeClips = track2.getActiveClips(currentTime)

        // 检查是否有活跃片段
        if (activeClips.length === 0) {
          // 没有活跃片段，静音所有音频源
          for (const [clipId, { gain }] of audioSources) {
            if (gain.gain.value > 0) {
              gain.gain.setValueAtTime(0, audioContext.currentTime)
            }
          }
          lastActiveClipId = ''
          return
        }

        const activeClip = activeClips[0] // 只处理第一个活跃片段
        const currentActiveClipId = activeClip.id

        // 如果活跃片段没有变化，不需要重新设置
        if (currentActiveClipId === lastActiveClipId) {
          return
        }
        // 先将所有音频源静音
        for (const [clipId, { gain }] of audioSources) {
          if (gain.gain.value > 0) {
            gain.gain.setValueAtTime(0, audioContext.currentTime)
          }
        }

        // 启用当前活跃片段的音频
        const audioSource = audioSources.get(currentActiveClipId)
        if (audioSource) {
          const targetGain = activeClip.muted ? 0 : activeClip.volume
          if (targetGain > 0) {
            audioSource.gain.gain.setValueAtTime(targetGain, audioContext.currentTime)
          } else {
          }
        }
        lastActiveClipId = currentActiveClipId
      }

      // 存储更新函数以便在录制期间调用
      ;(this as any).dynamicAudioUpdate = updateAudioSources

      // 初始更新
      updateAudioSources()

      return destination.stream
    } catch (error) {
      return null
    }
  }
}
